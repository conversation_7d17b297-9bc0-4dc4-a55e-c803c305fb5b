{"name": "@evolve/api-gateway", "version": "1.0.0", "description": "EVOLVE API Gateway - TypeScript/Node.js gateway for microservices orchestration", "private": true, "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "api-gateway:dev": "npm run dev", "api-gateway:start": "npm run start", "api-gateway:build": "npm run build", "api-gateway:test": "npm run test"}, "dependencies": {"@supabase/supabase-js": "^2.50.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "http-proxy-middleware": "^2.0.6", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "prettier": "^3.1.1", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "keywords": ["typescript", "nodejs", "api-gateway", "microservices", "backend", "express"], "author": "", "license": "ISC", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}