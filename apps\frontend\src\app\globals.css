@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark ([36m&:is(.dark )[39m*);

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-heading: var(--font-mission-script);
  --color-evolve-blue: var(--evolve-blue);
  --color-evolve-green: var(--evolve-green);
  --color-evolve-yellow: var(--evolve-yellow);
  --color-paper-texture: var(--paper-texture);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  
  /* EVOLVE Brand Colors */
  --evolve-blue: #007BFF;
  --evolve-green: #28A745;
  --evolve-yellow: #FFC107;
  --paper-texture: oklch(0.98 0 0);
  --font-mission-script: 'Mission Script', cursive;
  
  /* Core Colors */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
  
  /* EVOLVE Typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }
  
  /* Paper texture background */
  .paper-bg {
    background-color: var(--paper-texture);
    background-image: 
      radial-gradient(circle at 1px 1px, rgba(0,0,0,0.05) 1px, transparent 0);
    background-size: 20px 20px;
  }
  
  .bg-paper {
    background-color: var(--paper-texture);
  }
  
  .bg-paper-texture {
    background-color: var(--paper-texture);
    background-image: 
      radial-gradient(circle at 1px 1px, rgba(0,0,0,0.05) 1px, transparent 0);
    background-size: 20px 20px;
  }
  
  /* Line art style elements */
  .line-art {
    stroke: currentColor;
    stroke-width: 2px;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
  }
  
  /* Hand-drawn style buttons */
  .sketch-button {
    position: relative;
    border: 2px solid currentColor;
    border-radius: 0;
    background: transparent;
    box-shadow: 2px 2px 0 currentColor;
    transform: rotate(-0.5deg);
    transition: all 0.2s ease;
  }
  
  .sketch-button:hover {
    transform: rotate(0deg) translateY(-1px);
    box-shadow: 3px 3px 0 currentColor;
  }
  
  /* Hand-drawn style inputs */
  .sketch-border {
    position: relative;
    border-radius: 0;
    box-shadow: 1px 1px 0 currentColor;
  }
  
  .sketch-border:focus {
    box-shadow: 2px 2px 0 currentColor;
  }
  
  /* Torn edge effect */
  .torn-edge {
    position: relative;
  }
  
  .torn-edge::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 4px;
    background: repeating-linear-gradient(
      90deg,
      transparent,
      transparent 8px,
      currentColor 8px,
      currentColor 10px
    );
  }
  
  /* Notebook margin */
  .notebook-margin {
    border-left: 2px solid var(--evolve-blue);
    padding-left: 1rem;
    margin-left: 2rem;
  }
  
  /* Scribble animation */
  @keyframes scribble {
    0% { transform: translateX(0) rotate(0deg); }
    25% { transform: translateX(-1px) rotate(-0.5deg); }
    50% { transform: translateX(1px) rotate(0.5deg); }
    75% { transform: translateX(-1px) rotate(-0.5deg); }
    100% { transform: translateX(0) rotate(0deg); }
  }
  
  .scribble-hover:hover {
    animation: scribble 0.3s ease-in-out;
  }
}
