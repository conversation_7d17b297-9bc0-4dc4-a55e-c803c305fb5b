"""
Logging configuration for EVOLVE User Profile Service
"""

import logging
import logging.config
from typing import Dict, Any

from app.core.config import settings

def setup_logging() -> None:
    """Setup logging configuration."""
    
    logging_config: Dict[str, Any] = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "[%(asctime)s] %(levelname)s in %(module)s: %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "detailed": {
                "format": "[%(asctime)s] %(levelname)s in %(module)s (%(funcName)s:%(lineno)d): %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "default",
                "stream": "ext://sys.stdout",
            },
            "file": {
                "class": "logging.FileHandler",
                "level": "DEBUG" if settings.DEBUG else "INFO",
                "formatter": "detailed",
                "filename": "user_profile_service.log",
                "mode": "a",
            },
        },
        "loggers": {
            "app": {
                "level": "DEBUG" if settings.DEBUG else "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "level": "INFO" if settings.DATABASE_ECHO else "WARNING",
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
        },
        "root": {
            "level": "INFO",
            "handlers": ["console"],
        },
    }
    
    logging.config.dictConfig(logging_config)
    
    # Get the app logger
    logger = logging.getLogger("app")
    logger.info(f"Logging initialized for {settings.APP_NAME}")
    
    if settings.DEBUG:
        logger.debug("Debug mode enabled")
