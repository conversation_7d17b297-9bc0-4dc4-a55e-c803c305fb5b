"""
Authentication schemas for EVOLVE Auth Service
"""

from typing import Optional
from pydantic import BaseModel, EmailStr, Field


class LoginRequest(BaseModel):
    """Login request schema"""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=6, description="User password")


class LoginResponse(BaseModel):
    """Login response schema"""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: dict = Field(..., description="User information")


class TokenResponse(BaseModel):
    """Token response schema"""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class UserInfo(BaseModel):
    """User information schema"""
    id: str = Field(..., description="User ID")
    email: str = Field(..., description="User email")
    email_verified: bool = Field(..., description="Email verification status")
    created_at: str = Field(..., description="User creation timestamp")
    updated_at: Optional[str] = Field(None, description="User update timestamp")
    user_metadata: Optional[dict] = Field(None, description="User metadata")


class ErrorResponse(BaseModel):
    """Error response schema"""
    error: str = Field(..., description="Error type")
    error_description: str = Field(..., description="Error description")
    status_code: int = Field(..., description="HTTP status code")


class OAuthURLResponse(BaseModel):
    """OAuth URL response schema"""
    provider: str = Field(..., description="OAuth provider (google, github)")
    auth_url: str = Field(..., description="OAuth authorization URL")
    state: Optional[str] = Field(None, description="OAuth state parameter")


class RefreshTokenRequest(BaseModel):
    """Refresh token request schema"""
    refresh_token: str = Field(..., description="Refresh token")


class LogoutRequest(BaseModel):
    """Logout request schema"""
    access_token: str = Field(..., description="Access token to invalidate")


class EmailVerificationRequest(BaseModel):
    """Email verification request schema"""
    access_token: str = Field(..., description="Access token from verification email")
    refresh_token: str = Field(..., description="Refresh token from verification email")
    token_type: Optional[str] = Field(default="bearer", description="Token type")
    expires_in: Optional[int] = Field(None, description="Token expiration time")
    expires_at: Optional[int] = Field(None, description="Token expiration timestamp")
    type: Optional[str] = Field(None, description="Verification type")


class EmailVerificationResponse(BaseModel):
    """Email verification response schema"""
    message: str = Field(..., description="Verification message")
    access_token: str = Field(..., description="Access token")
    refresh_token: str = Field(..., description="Refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: dict = Field(..., description="User information")
    redirect_url: str = Field(..., description="URL to redirect user to")


class PasswordResetRequest(BaseModel):
    """Password reset request schema"""
    email: EmailStr = Field(..., description="User email address")


class PasswordUpdateRequest(BaseModel):
    """Password update request schema"""
    access_token: str = Field(..., description="Access token from password reset link")
    new_password: str = Field(..., min_length=6, description="New user password")


class PasswordResetResponse(BaseModel):
    """Password reset response schema"""
    message: str = Field(..., description="Password reset message")


class SignUpRequest(BaseModel):
    """Sign up request schema"""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=6, description="User password")
    full_name: Optional[str] = Field(None, description="User full name")
    metadata: Optional[dict] = Field(None, description="Additional user metadata")


class SignUpResponse(BaseModel):
    """Sign up response schema"""
    message: str = Field(..., description="Sign up message")
    user: Optional[dict] = Field(None, description="User information (if email confirmation disabled)")
    session: Optional[dict] = Field(None, description="Session information (if email confirmation disabled)")
    confirmation_sent: bool = Field(..., description="Whether confirmation email was sent")
