import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { authenticateToken, optionalAuthentication } from './middleware/auth';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // Enable CORS
app.use(morgan('combined')); // Logging
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'api-gateway'
  });
});

// Service proxy configurations with authentication requirements
const services = {
  ai: {
    target: process.env.AI_SERVICE_URL || 'http://localhost:8002',
    pathRewrite: { '^/api/ai': '' },
    requireAuth: true, // AI service requires authentication
    description: 'AI and ML operations'
  },
  auth: {
    target: process.env.AUTH_SERVICE_URL || 'http://localhost:8001',
    pathRewrite: { '^/api/auth': '/auth' },
    requireAuth: false, // Auth service is public
    description: 'Authentication and authorization'
  },
  content: {
    target: process.env.CONTENT_SERVICE_URL || 'http://localhost:8003',
    pathRewrite: { '^/api/content': '' },
    requireAuth: true, // Content service requires authentication
    description: 'Content management and delivery'
  },
  user: {
    target: process.env.USER_SERVICE_URL || 'http://localhost:8003',
    pathRewrite: { '^/api/user': '' },
    requireAuth: true, // User service requires authentication
    description: 'User profile and preferences'
  },
  profiles: {
    target: process.env.USER_SERVICE_URL || 'http://localhost:8003',
    pathRewrite: { '^/api/profiles': '' },
    requireAuth: true, // Profiles service requires authentication
    description: 'User profiles management'
  },
  notification: {
    target: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:8005',
    pathRewrite: { '^/api/notification': '' },
    requireAuth: true, // Notification service requires authentication
    description: 'Real-time notifications'
  }
};

// Create proxy middleware with conditional authentication
const createAuthenticatedProxy = (serviceName: string, config: any) => {
  const middlewares = [];
  
  // Add authentication middleware for protected services
  if (config.requireAuth) {
    middlewares.push(authenticateToken);
    console.log(`🔒 Applied JWT authentication to ${serviceName} service`);
  } else {
    console.log(`🔓 ${serviceName} service is publicly accessible`);
  }
  
  // Add the proxy middleware
  middlewares.push(createProxyMiddleware({
    target: config.target,
    changeOrigin: true,
    pathRewrite: config.pathRewrite,
    onError: (err, req, res) => {
      console.error(`❌ Proxy error for ${serviceName}:`, err.message);
      res.status(502).json({
        error: 'Service temporarily unavailable',
        error_description: `The ${serviceName} service is currently unavailable. Please try again later.`,
        service: serviceName,
        timestamp: new Date().toISOString()
      });
    },
    onProxyReq: (proxyReq, req, res) => {
      const userInfo = (req as any).user ? `(User: ${(req as any).user.sub})` : '(Unauthenticated)';
      console.log(`🔄 Proxying ${req.method} ${req.url} to ${serviceName} service ${userInfo}`);
      
      // Forward user information in headers if authenticated
      if ((req as any).user) {
        proxyReq.setHeader('X-User-ID', (req as any).user.sub);
        proxyReq.setHeader('X-User-Email', (req as any).user.email || '');
        proxyReq.setHeader('X-Authenticated', 'true');
      }
    },
    onProxyRes: (proxyRes, req, res) => {
      // Log successful proxy responses
      console.log(`✅ ${serviceName} service responded with status: ${proxyRes.statusCode}`);
    }
  }));
  
  return middlewares;
};

// Setup proxy middleware for each service with conditional authentication
Object.entries(services).forEach(([serviceName, config]) => {
  const proxyPath = `/api/${serviceName}`;
  const middlewares = createAuthenticatedProxy(serviceName, config);
  
  // Apply all middlewares to the route
  app.use(proxyPath, ...middlewares);
});

// API routes
app.get('/api', (req, res) => {
  res.json({
    message: 'EVOLVE API Gateway',
    version: '1.0.0',
    services: Object.keys(services),
    endpoints: {
      health: '/health',
      ai: '/api/ai',
      auth: '/api/auth',
      content: '/api/content',
      user: '/api/user',
      profiles: '/api/profiles'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// Global error handler
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error:', err);
  res.status(500).json({
    error: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 API Gateway running on port ${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API info: http://localhost:${PORT}/api`);
  console.log(`🔗 Available services: ${Object.keys(services).join(', ')}`);
});

export default app;
