#!/usr/bin/env python3
"""
Test script to verify Supabase signup and email confirmation functionality
"""

import asyncio
import os
import sys
import time
from typing import Dict, Optional, <PERSON><PERSON>

# Add the auth service to the path
sys.path.append('apps/auth-service')

from supabase import create_client, Client
from gotrue.errors import AuthApiError

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')
SUPABASE_SERVICE_ROLE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

def test_supabase_connection():
    """Test basic Supabase connection"""
    print("Testing Supabase connection...")

    if not SUPABASE_URL or not SUPABASE_ANON_KEY:
        print("❌ Missing Supabase environment variables")
        return False

    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
        print(f"✅ Connected to Supabase: {SUPABASE_URL}")
        return True
    except Exception as e:
        print(f"❌ Failed to connect to Supabase: {e}")
        return False

def test_signup_flow():
    """Test the signup flow with email confirmation"""
    print("\nTesting signup flow...")

    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)

        # Test email for signup (use a real email format)
        test_email = f"test.user.{int(time.time())}@gmail.com"
        test_password = "TestPassword123!"

        print(f"Attempting signup for: {test_email}")

        response = supabase.auth.sign_up({
            "email": test_email,
            "password": test_password,
            "options": {
                "data": {
                    "full_name": "Test User"
                },
                "emailRedirectTo": "http://localhost:3000/auth/callback"
            }
        })

        if response.user:
            print(f"✅ User created: {response.user.id}")
            print(f"   Email: {response.user.email}")
            print(f"   Email confirmed: {response.user.email_confirmed_at is not None}")

            if response.session:
                print("✅ Session created (email confirmation disabled)")
                return True
            else:
                print("✅ Email confirmation required (this is expected)")
                print("   Check your email for verification link")
                return True
        else:
            print("❌ No user returned from signup")
            return False

    except AuthApiError as e:
        if "User already registered" in str(e):
            print(f"ℹ️  User already exists: {test_email}")
            return True
        else:
            print(f"❌ Supabase Auth API error: {e}")
            return False
    except Exception as e:
        print(f"❌ Unexpected error during signup: {e}")
        return False

def check_auth_settings():
    """Check Supabase auth settings"""
    print("\nChecking Supabase auth settings...")

    try:
        # Use service role to check settings
        supabase_admin = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

        print("✅ Service role connection successful")
        print("ℹ️  To check email confirmation settings:")
        print("   1. Go to your Supabase dashboard")
        print("   2. Navigate to Authentication > Settings")
        print("   3. Check 'Enable email confirmations' setting")
        print("   4. Verify email templates are configured")

        return True
    except Exception as e:
        print(f"❌ Failed to connect with service role: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 EVOLVE Signup Test Script")
    print("=" * 50)

    # Test connection
    if not test_supabase_connection():
        return

    # Check auth settings
    check_auth_settings()

    # Test signup flow
    test_signup_flow()

    print("\n" + "=" * 50)
    print("📧 Email Configuration Notes:")
    print("1. Ensure email confirmation is enabled in Supabase dashboard")
    print("2. Configure email templates if needed")
    print("3. For production, set up custom SMTP settings")
    print("4. Test with a real email address to receive confirmation emails")

if __name__ == "__main__":
    main()
