"use client"

import React, { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON>ard, PaperCardHeader, PaperCardTitle, PaperCardContent } from "@/app/components/common/PaperCard"
import { SketchButton } from "@/app/components/common/SketchButton"
import { Logo } from "@/app/components/common/Logo"
import { 
  getAuthState, 
  isSessionValid, 
  signOut, 
  refreshSession, 
  onAuthStateChange,
  type AuthState 
} from "@/lib/auth"

export default function DashboardPage() {
  const router = useRouter()
  const [authState, setAuthState] = useState<AuthState | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isValidating, setIsValidating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [sessionRefreshAttempts, setSessionRefreshAttempts] = useState(0)

  useEffect(() => {
    let mounted = true
    let authStateUnsubscribe: (() => void) | null = null

    const checkAuthenticationState = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        // Get comprehensive auth state
        const currentAuthState = await getAuthState()
        
        if (!mounted) return
        
        if (!currentAuthState.isAuthenticated) {
          // Not authenticated, redirect to login
          router.push("/login?error=not_authenticated")
          return
        }

        // Validate the session to ensure it's still valid
        setIsValidating(true)
        const isValid = await isSessionValid()
        
        if (!mounted) return
        
        if (!isValid) {
          // Session is invalid, try to refresh it
          if (sessionRefreshAttempts < 2) {
            setSessionRefreshAttempts(prev => prev + 1)
            const refreshedToken = await refreshSession()
            
            if (refreshedToken) {
              // Session refreshed successfully, get updated auth state
              const updatedAuthState = await getAuthState()
              setAuthState(updatedAuthState)
            } else {
              // Session refresh failed, redirect to login
              router.push("/login?error=session_expired")
              return
            }
          } else {
            // Too many refresh attempts, redirect to login
            router.push("/login?error=session_refresh_failed")
            return
          }
        } else {
          // Session is valid, set auth state
          setAuthState(currentAuthState)
        }
      } catch (err) {
        console.error('Error checking authentication state:', err)
        if (mounted) {
          setError('Failed to verify authentication')
          // On error, redirect to login after a short delay
          setTimeout(() => {
            router.push("/login?error=auth_check_failed")
          }, 2000)
        }
      } finally {
        if (mounted) {
          setIsLoading(false)
          setIsValidating(false)
        }
      }
    }

    // Set up auth state change listener
    authStateUnsubscribe = onAuthStateChange((newAuthState) => {
      if (mounted) {
        setAuthState(newAuthState)
        
        // If user is signed out, redirect to login
        if (!newAuthState.isAuthenticated) {
          router.push("/login?error=signed_out")
        }
      }
    })

    checkAuthenticationState()

    return () => {
      mounted = false
      if (authStateUnsubscribe) {
        authStateUnsubscribe()
      }
    }
  }, [router, sessionRefreshAttempts])

  const handleLogout = async () => {
    try {
      setIsLoading(true)
      // Use unified signOut function that handles both auth methods
      await signOut()
      router.push("/login")
    } catch (err) {
      console.error('Error during logout:', err)
      // Even if logout fails, redirect to login
      router.push("/login")
    }
  }

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen paper-bg flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="mb-4">
            <Logo size="lg" showText={true} className="justify-center" />
          </div>
          <div className="w-8 h-8 mx-auto animate-spin rounded-full border-2 border-primary border-t-transparent" />
          <p className="text-muted-foreground font-sans">
            {isValidating ? 'Validating session...' : 'Loading dashboard...'}
          </p>
          {authState && (
            <p className="text-sm text-muted-foreground font-sans">
              Authentication: {authState.authMethod || 'detecting'}
            </p>
          )}
        </div>
      </div>
    )
  }

  // Show error state if authentication check failed
  if (error) {
    return (
      <div className="min-h-screen paper-bg flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 mx-auto rounded-full bg-red-100 flex items-center justify-center">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h2 className="text-xl font-heading text-red-600">Authentication Error</h2>
          <p className="text-muted-foreground font-sans">{error}</p>
          <p className="text-sm text-muted-foreground font-sans">Redirecting to login...</p>
        </div>
      </div>
    )
  }

  // Ensure we have valid auth state before rendering dashboard
  if (!authState || !authState.isAuthenticated || !authState.user) {
    return (
      <div className="min-h-screen paper-bg flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="mb-4">
            <Logo size="lg" showText={true} className="justify-center" />
          </div>
          <div className="w-8 h-8 mx-auto animate-spin rounded-full border-2 border-primary border-t-transparent" />
          <p className="text-muted-foreground font-sans">Preparing dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen paper-bg">
      {/* Header */}
      <header className="border-b-2 border-dashed border-gray-300 bg-background/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Logo size="md" showText={true} />
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground font-sans">
                Welcome, {authState.user?.email || "User"}
              </span>
              <div className="flex items-center space-x-2">
                <span className="text-xs px-2 py-1 rounded-full bg-primary/10 text-primary font-mono">
                  {authState.authMethod}
                </span>
                <SketchButton
                  onClick={handleLogout}
                  variant="outline"
                  size="sm"
                  className="scribble-hover"
                >
                  Logout
                </SketchButton>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex items-center justify-center px-4 py-16">
        <div className="w-full max-w-2xl text-center">
          <PaperCard className="shadow-xl transform rotate-[-0.3deg] hover:rotate-0 transition-transform duration-500">
            <PaperCardHeader className="text-center pb-8">
              <div className="mb-6">
                {/* Decorative line art */}
                <svg 
                  className="w-24 h-24 mx-auto text-primary line-art" 
                  viewBox="0 0 100 100"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  {/* Simple butterfly/evolution symbol */}
                  <path d="M50 20 C30 30, 30 50, 50 50 C70 50, 70 30, 50 20 Z" />
                  <path d="M50 50 C30 60, 30 80, 50 80 C70 80, 70 60, 50 50 Z" />
                  <line x1="50" y1="20" x2="50" y2="80" />
                  <circle cx="50" cy="35" r="2" fill="currentColor" />
                  <circle cx="50" cy="65" r="2" fill="currentColor" />
                </svg>
              </div>
              
              <PaperCardTitle className="text-4xl md:text-5xl font-heading mb-4 text-primary">
                Get Ready to Evolve!
              </PaperCardTitle>
              
              <p className="text-lg text-muted-foreground font-sans max-w-md mx-auto leading-relaxed">
                Welcome to your transformation journey. Your evolution starts here, one step at a time.
              </p>
            </PaperCardHeader>

            <PaperCardContent className="text-center space-y-6">
              {/* User info section */}
              <div className="bg-muted/30 rounded-lg p-4 border-2 border-dashed border-gray-300">
                <h3 className="font-heading text-lg mb-2">Your Evolution Profile</h3>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <p><strong>Email:</strong> {authState.user?.email}</p>
                  <p><strong>Member Since:</strong> {authState.user?.created_at ? new Date(authState.user.created_at).toLocaleDateString() : "Today"}</p>
                  <p><strong>Status:</strong> 
                    <span className={`ml-1 ${authState.user?.email_verified ? 'text-green-600' : 'text-yellow-600'}`}>
                      {authState.user?.email_verified ? "Verified" : "Pending Verification"}
                    </span>
                  </p>
                  <p><strong>Auth Method:</strong> 
                    <span className="ml-1 capitalize text-primary font-medium">
                      {authState.authMethod}
                    </span>
                  </p>
                </div>
              </div>

              {/* Call to action */}
              <div className="space-y-4">
                <p className="text-muted-foreground font-sans">
                  Ready to begin your journey? Let&apos;s start building something amazing together.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <SketchButton
                    className="px-6 py-3 scribble-hover"
                    onClick={() => {/* Future: Navigate to main app */}}
                  >
                    Start Evolving
                  </SketchButton>
                  
                  <SketchButton
                    variant="outline"
                    className="px-6 py-3 scribble-hover"
                    onClick={() => {/* Future: Navigate to profile */}}
                  >
                    View Profile
                  </SketchButton>
                </div>
              </div>

              {/* Coming soon section */}
              <div className="pt-6 border-t border-dashed border-gray-300">
                <p className="text-xs text-muted-foreground font-sans">
                  This is just the beginning. More features coming soon to help you evolve and grow.
                </p>
              </div>
            </PaperCardContent>
          </PaperCard>
        </div>
      </main>
    </div>
  )
}
