"""
Custom Exception Classes

This module defines custom exception classes for the EVOLVE AI Service.
"""

from typing import Any, Dict, Optional


class EvolveException(Exception):
    """
    Base exception class for EVOLVE AI Service.
    """
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(message)


class ValidationError(EvolveException):
    """
    Exception raised when data validation fails.
    """
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=400, details=details)


class AIServiceError(EvolveException):
    """
    Exception raised when AI service operations fail.
    """
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)


class DatabaseError(EvolveException):
    """
    Exception raised when database operations fail.
    """
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)


class AuthenticationError(EvolveException):
    """
    Exception raised when authentication fails.
    """
    
    def __init__(self, message: str = "Authentication failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=401, details=details)


class AuthorizationError(EvolveException):
    """
    Exception raised when authorization fails.
    """
    
    def __init__(self, message: str = "Authorization failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=403, details=details)


class NotFoundError(EvolveException):
    """
    Exception raised when a requested resource is not found.
    """
    
    def __init__(self, message: str = "Resource not found", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=404, details=details)
