import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { cn } from "@/app/lib/utils"

interface PaperCardProps extends React.ComponentProps<typeof Card> {
  paperTexture?: boolean
  tornEdge?: boolean
  notebookMargin?: boolean
}

/**
 * PaperCard - A paper-themed card component for EVOLVE
 * Extends shadcn/ui Card with paper texture and hand-drawn styling
 */
export function PaperCard({
  className,
  paperTexture = true,
  tornEdge = false,
  notebookMargin = false,
  ...props
}: PaperCardProps) {
  return (
    <Card
      className={cn(
        "border-2",
        "border-gray-300",
        "shadow-lg",
        "transform",
        "rotate-[-0.2deg]",
        "hover:rotate-0",
        "transition-transform",
        "duration-300",
        "ease-in-out",
        paperTexture && "paper-bg",
        tornEdge && "torn-edge",
        notebookMargin && "notebook-margin",
        className
      )}
      {...props}
    />
  )
}

/**
 * PaperCardHeader - Header component for PaperCard
 */
export function PaperCardHeader({ className, ...props }: React.ComponentProps<typeof CardHeader>) {
  return (
    <CardHeader
      className={cn(
        "border-b-2",
        "border-dashed",
        "border-gray-300",
        "pb-4",
        className
      )}
      {...props}
    />
  )
}

/**
 * PaperCardTitle - Title component for PaperCard with heading font
 */
export function PaperCardTitle({ className, ...props }: React.ComponentProps<typeof CardTitle>) {
  return (
    <CardTitle
      className={cn(
        "font-heading",
        "text-xl",
        "text-gray-900",
        "mb-2",
        className
      )}
      {...props}
    />
  )
}

/**
 * PaperCardDescription - Description component for PaperCard
 */
export function PaperCardDescription({ className, ...props }: React.ComponentProps<typeof CardDescription>) {
  return (
    <CardDescription
      className={cn(
        "text-gray-600",
        "font-sans",
        "text-sm",
        className
      )}
      {...props}
    />
  )
}

/**
 * PaperCardContent - Content component for PaperCard
 */
export function PaperCardContent({ className, ...props }: React.ComponentProps<typeof CardContent>) {
  return (
    <CardContent
      className={cn(
        "space-y-4",
        className
      )}
      {...props}
    />
  )
}

