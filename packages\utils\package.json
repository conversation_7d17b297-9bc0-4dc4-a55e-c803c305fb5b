{"name": "@evolve/utils", "version": "1.0.0", "description": "Shared utility functions for EVOLVE platform", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "src"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "clean": "rm -rf dist .turbo node_modules"}, "dependencies": {"clsx": "^2.1.0", "date-fns": "^3.3.1", "lodash": "^4.17.21", "zod": "^3.22.4"}, "devDependencies": {"@types/lodash": "^4.14.202", "tsup": "^8.0.2", "typescript": "^5.3.3", "vitest": "^1.2.2"}, "keywords": ["typescript", "utils", "utilities", "shared", "functions"], "author": "EVOLVE Team", "license": "MIT", "publishConfig": {"access": "restricted"}}