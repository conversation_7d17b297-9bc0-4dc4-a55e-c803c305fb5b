"use client"

import React, { useState, useEffect, useCallback, useRef } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"

import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { ErrorMessage } from "@/app/components/auth/ErrorMessage"
import { LoadingSpinner } from "@/app/components/auth/LoadingSpinner"
import { SketchButton } from "@/app/components/common/SketchButton"
import { SocialLoginButton } from "@/app/components/common/SocialLoginButton"
import { PaperCard, PaperCardHeader, PaperCardTitle, PaperCardContent } from "@/app/components/common/PaperCard"
import { Logo } from "@/app/components/common/Logo"
import { useAuth } from "@/app/hooks/useAuth"
import { useAuthToast } from "@/app/components/ui/toast-provider"

interface LoginFormData {
  email: string
  password: string
  rememberMe?: boolean
}

export default function LoginPage() {
const router = useRouter()
const auth = useAuth({ showToasts: true })
const [isNavigating, setIsNavigating] = useState(false)

const navigateTo = useCallback((path: string) => {
  setIsNavigating(true)
  router.push(path)
}, [router])
  const toast = useAuthToast()
  
  const [formData, setFormData] = useState<LoginFormData>({
    email: "",
    password: "",
    rememberMe: false,
  })
  const [errors, setErrors] = useState<Partial<LoginFormData>>({})
  const [showRetryInfo, setShowRetryInfo] = useState(false)

  const validateForm = (): boolean => {
    const newErrors: Partial<LoginFormData> = {}

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error for this field when user starts typing
    if (errors[name as keyof LoginFormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }))
    }
  }

const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    console.log('Login form submitted', { formData })
    
    if (!validateForm()) {
      console.log('Form validation failed', errors)
      return
    }

    console.log('Form validation passed')

    if (auth.isAuthenticated) {
      console.log('User already authenticated, redirecting to dashboard')
      navigateTo('/dashboard')
      return
    }

    console.log('Starting login process...')
    
    // Clear any previous errors
    auth.clearError()
    setShowRetryInfo(false)

    try {
      console.log('Calling auth.login with:', { email: formData.email })
      const success = await auth.login(formData.email, formData.password)
      
      console.log('Login result:', success)
      
      if (success) {
        console.log('Login successful, clearing form and redirecting')
        // Clear form on successful login
        setFormData({
          email: "",
          password: "",
        })
        
        // Redirect to dashboard with a small delay to prevent race conditions
        setTimeout(() => {
          navigateTo('/dashboard')
        }, 100)
      } else {
        console.log('Login failed, checking for errors')
        if (auth.error) {
          console.log('Auth error:', auth.error)
          // Show retry information for retryable errors
          if (auth.error.retryable && auth.canRetry()) {
            setShowRetryInfo(true)
          }
        }
      }
    } catch (error) {
      console.error('Login error caught:', error)
    }
  }
  
  const handleRetry = async () => {
    setShowRetryInfo(false)
    const success = await auth.retryLastOperation()
    
    if (success) {
      setFormData({ email: "", password: "" })
      setTimeout(() => {
        navigateTo('/dashboard')
      }, 100)
    } else if (auth.error?.retryable && auth.canRetry()) {
      setShowRetryInfo(true)
    }
  }

useEffect(() => {
    return () => setIsNavigating(false)
  }, [])

  return (
    <div className="min-h-screen paper-bg flex items-center justify-center px-4 py-8">
      <div className="w-full max-w-md">
        <PaperCard className="shadow-xl">
          <PaperCardHeader className="text-center">
            <div className="mb-6">
              <Logo size="lg" showText={true} className="justify-center" />
            </div>
            <PaperCardTitle className="text-3xl font-heading mb-2">
              Welcome Back
            </PaperCardTitle>
            <p className="text-muted-foreground font-sans">
              Sign in to continue your evolution journey
            </p>
          </PaperCardHeader>

          <PaperCardContent>
            {/* Error Display with Retry Options */}
            {auth.error && (
              <ErrorMessage 
                message={auth.error.userMessage} 
                showRetry={showRetryInfo && auth.canRetry()} 
                onRetry={handleRetry} 
                retryLabel={auth.isLoading ? 'Retrying...' : 'Retry Now'} 
                className="mb-6"
              />
            )}

            {/* Social Login Buttons */}
            <div className="space-y-3 mb-6">
              <SocialLoginButton 
                provider="google" 
                mode="login"
                disabled={auth.isLoading || isNavigating}
                onError={(error) => {
                  toast.showError({
                    code: 'OAUTH_ERROR',
                    message: error,
                    userMessage: error,
                    retryable: true
                  })
                }}
                onLoadingChange={() => {}}
              />
              <SocialLoginButton 
                provider="github" 
                mode="login"
                disabled={auth.isLoading || isNavigating}
                onError={(error) => {
                  toast.showError({
                    code: 'OAUTH_ERROR',
                    message: error,
                    userMessage: error,
                    retryable: true
                  })
                }}
                onLoadingChange={() => {}}
              />
            </div>

            {/* Divider */}
            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-dashed border-gray-300" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground font-sans">
                  Or continue with email
                </span>
              </div>
            </div>

            <form onSubmit={handleLogin} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleInputChange}
                  disabled={auth.isLoading}
                  className={errors.email ? "border-destructive" : ""}
                />
                {errors.email && (
                  <p className="text-sm text-destructive">{errors.email}</p>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/forgot-password"
                    className="text-sm text-primary hover:underline scribble-hover"
                  >
                    Forgot password?
                  </Link>
                </div>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Enter your password"
                  value={formData.password}
                  onChange={handleInputChange}
                  disabled={auth.isLoading}
                  className={errors.password ? "border-destructive" : ""}
                />
                {errors.password && (
                  <p className="text-sm text-destructive">{errors.password}</p>
                )}
              </div>

              {/* Remember Me Checkbox */}
              <div className="flex items-center space-x-2">
                <input
                  id="rememberMe"
                  name="rememberMe"
                  type="checkbox"
                  checked={formData.rememberMe}
                  onChange={(e) => setFormData(prev => ({ ...prev, rememberMe: e.target.checked }))}
                  className="rounded border-gray-300 text-primary focus:ring-primary"
                />
                <Label htmlFor="rememberMe" className="text-sm cursor-pointer">
                  Remember me
                </Label>
              </div>

              <SketchButton
                type="submit"
                disabled={auth.isLoading || isNavigating}
                className="w-full py-3 text-lg font-medium"
              >
                {isNavigating ? "Redirecting..." : auth.isLoading ? "Signing In..." : "Sign In"}
              </SketchButton>
            </form>

            <div className="mt-6 text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                Don&apos;t have an account?{" "}
                <Link 
                  href="/signup" 
                  className="text-primary hover:underline font-medium scribble-hover"
                  onClick={(e) => isNavigating && e.preventDefault()}
                >
                  Create one here
                </Link>
              </p>
              
              <div className="pt-4 border-t border-dashed border-gray-300">
                <p className="text-xs text-muted-foreground">
                  Secure login powered by Evolve
                </p>
              </div>
            </div>
          </PaperCardContent>
        </PaperCard>
      </div>
    </div>
  )
}
