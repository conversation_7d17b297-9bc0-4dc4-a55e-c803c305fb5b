import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from "next/router";
import { signIn, signOut, signUp, refreshSession, getAuthState, onAuthStateChange, type AuthState } from '@/lib/auth';
import { AuthError } from '@/lib/authErrors';

// Define the types for user data and authentication states
interface User {
  id: string;
  email: string;
  email_verified: boolean;
  created_at: string;
  updated_at?: string;
  user_metadata?: Record<string, unknown>;
}

interface AuthContextProps {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: AuthError | null;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, fullName: string) => Promise<void>;
  logout: () => void;
  refreshAuthSession: () => Promise<void>;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<AuthError | null>(null);

  const router = useRouter();

  useEffect(() => {
    const initializeAuth = async () => {
      const authState = getAuthState();
      setUser(authState.user);
      setIsAuthenticated(authState.isAuthenticated);
      setIsLoading(false);

      onAuthStateChange((newState) => {
        setUser(newState.user);
        setIsAuthenticated(newState.isAuthenticated);
      });
    };
    initializeAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);
    try {
      await signIn(email, password);
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (email: string, password: string, fullName: string) => {
    setIsLoading(true);
    setError(null);
    try {
      await signUp(email, password, fullName);
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    await signOut();
    setUser(null);
    setIsAuthenticated(false);
  };

  const refreshAuthSession = async () => {
    setIsLoading(true);
    try {
      await refreshSession();
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, isAuthenticated, isLoading, error, login, signup, logout, refreshAuthSession }}>
      {children}
    </AuthContext.Provider>
  );
};

