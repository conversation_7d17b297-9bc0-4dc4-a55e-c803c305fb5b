{"name": "evolve-monorepo", "version": "1.0.0", "description": "EVOLVE AI IDE - Monorepo for Next.js frontend, FastAPI backend services, and shared packages", "private": true, "scripts": {"build": "turbo build", "build:shared": "turbo build --filter=./packages/*", "build:apps": "turbo build --filter=./apps/*", "build:services": "turbo build --filter=./services/*", "dev": "turbo dev", "dev:shared": "turbo dev --filter=./packages/*", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "test": "turbo test", "test:watch": "turbo test:watch", "clean": "turbo clean", "typecheck": "turbo type-check", "format": "turbo format", "format:check": "turbo format:check"}, "keywords": ["monorepo", "ai", "education", "nextjs", "<PERSON><PERSON><PERSON>"], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.1", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "dependencies": {"turbo": "^2.5.4"}}