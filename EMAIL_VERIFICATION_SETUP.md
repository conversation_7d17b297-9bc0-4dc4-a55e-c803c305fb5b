# Email Verification Setup for EVOLVE

This document explains how to configure and troubleshoot email verification for user signup in the EVOLVE application.

## Overview

The EVOLVE application uses Supabase for authentication with email verification enabled. When users sign up, they receive a verification email that they must click to activate their account.

## Configuration Steps

### 1. Supabase Dashboard Configuration

1. **Go to your Supabase Dashboard**
   - Navigate to: https://supabase.com/dashboard
   - Select your project: `lfcgejqzjephajlpmfgv`

2. **Enable Email Confirmation**
   - Go to `Authentication` > `Settings`
   - Under "User Signups", ensure "Enable email confirmations" is **ENABLED**
   - Set "Confirm email" to **REQUIRED**

3. **Configure Redirect URLs**
   - In the same settings page, add these URLs to "Redirect URLs":
     - `http://localhost:3000/auth/callback`
     - `https://yourdomain.com/auth/callback` (for production)

4. **Email Templates (Optional)**
   - Go to `Authentication` > `Email Templates`
   - Customize the "Confirm signup" template if needed
   - Default template should work fine for testing

### 2. Environment Variables

Ensure these environment variables are set:

```bash
# .env
SUPABASE_URL=https://lfcgejqzjephajlpmfgv.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
FRONTEND_URL=http://localhost:3000

# apps/frontend/.env.local
NEXT_PUBLIC_SUPABASE_URL=https://lfcgejqzjephajlpmfgv.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
NEXT_PUBLIC_EMAIL_REDIRECT_URL=http://localhost:3000/auth/callback
```

### 3. Code Configuration

The following files have been updated to support email verification:

- `apps/frontend/src/lib/supabaseClient.ts` - Supabase client configuration
- `apps/frontend/src/lib/auth.ts` - Signup function with email confirmation
- `apps/auth-service/app/services/supabase_service.py` - Backend signup handling
- `apps/frontend/src/app/signup/page.tsx` - Frontend signup page
- `apps/frontend/src/app/auth/callback/page.tsx` - Email verification callback

## How It Works

### 1. User Signup Flow

1. User fills out signup form on `/signup`
2. Frontend calls `auth.signup()` with email, password, and full name
3. Supabase creates user account but keeps it unconfirmed
4. Supabase sends verification email to user's email address
5. User sees success message: "Check your email for verification link"

### 2. Email Verification Flow

1. User receives email with verification link
2. User clicks link, which redirects to `/auth/callback?code=...`
3. Callback page exchanges code for session using PKCE flow
4. User is logged in and redirected to dashboard

### 3. Error Handling

- Invalid/expired verification links show error message
- Users can request new verification emails
- Clear error messages guide users through the process

## Testing

### 1. Run the Test Script

```bash
python test_signup.py
```

This script will:
- Test Supabase connection
- Attempt a signup with test credentials
- Verify email confirmation is working

### 2. Manual Testing

1. Go to `http://localhost:3000/signup`
2. Fill out the form with a real email address
3. Click "Create New Account"
4. Check your email for verification link
5. Click the verification link
6. Verify you're redirected to dashboard and logged in

## Troubleshooting

### Common Issues

1. **No verification email received**
   - Check spam/junk folder
   - Verify email confirmation is enabled in Supabase dashboard
   - Check Supabase logs for email delivery issues

2. **Verification link doesn't work**
   - Ensure redirect URLs are configured correctly
   - Check that callback page is working: `/auth/callback`
   - Verify PKCE flow is enabled

3. **User can't sign in after verification**
   - Check that email_confirmed_at is set in Supabase
   - Verify session handling in callback page
   - Check browser console for errors

### Debug Steps

1. **Check Supabase Dashboard**
   - Go to `Authentication` > `Users`
   - Find the user and check `email_confirmed_at` field
   - Check `Authentication` > `Logs` for errors

2. **Check Browser Console**
   - Open developer tools during signup/verification
   - Look for JavaScript errors or network failures

3. **Check Network Requests**
   - Monitor network tab during signup
   - Verify API calls are successful (200 status)

## Production Considerations

### 1. Custom SMTP (Recommended)

For production, configure custom SMTP in Supabase:
- Go to `Settings` > `Auth` > `SMTP Settings`
- Configure your email provider (SendGrid, Mailgun, etc.)
- Test email delivery

### 2. Custom Email Templates

- Customize email templates to match your brand
- Include your company logo and styling
- Update email copy to be user-friendly

### 3. Rate Limiting

- Configure rate limiting for signup attempts
- Implement CAPTCHA for additional security
- Monitor for abuse patterns

## Support

If you continue to have issues:

1. Check Supabase documentation: https://supabase.com/docs/guides/auth
2. Review Supabase community forums
3. Check the EVOLVE project documentation
4. Contact the development team

## Recent Changes

The following changes were made to fix email verification:

1. **Updated Supabase client configuration** to use PKCE flow
2. **Enhanced signup function** to properly handle email confirmation
3. **Improved backend service** to set correct redirect URLs
4. **Updated frontend UI** to show better confirmation messages
5. **Added comprehensive error handling** for verification flow

All changes maintain backward compatibility and don't affect existing functionality.
