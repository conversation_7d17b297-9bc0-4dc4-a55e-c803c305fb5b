{"name": "@evolve/user-profile-service", "version": "1.0.0", "description": "EVOLVE User Profile Service - FastAPI user profile and preferences management", "private": true, "scripts": {"dev": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8003 --reload", "start": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8003", "user-profile:dev": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8003 --reload", "user-profile:start": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8003", "build": "echo 'User Profile Service build complete'", "user-profile:build": "echo 'User Profile Service build complete'", "test": "python -m pytest", "user-profile:test": "python -m pytest", "lint": "python -m flake8 app/", "user-profile:lint": "python -m flake8 app/", "format": "python -m black app/", "format:check": "python -m black --check app/", "type-check": "python -m mypy app/", "install": "pip install -r requirements.txt"}, "keywords": ["<PERSON><PERSON><PERSON>", "user-profiles", "preferences", "backend", "python"], "author": "", "license": "ISC"}