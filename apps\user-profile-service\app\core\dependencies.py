"""
Dependency injection for EVOLVE User Profile Service
"""

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from typing import AsyncGenerator

from app.core.database import get_async_session
from app.services.user_profile_service import UserProfileService, create_user_profile_service

# Database session dependency
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session dependency."""
    async for session in get_async_session():
        yield session

# Service dependencies
def get_user_profile_service() -> UserProfileService:
    """Get user profile service dependency."""
    return create_user_profile_service()

# Combined dependencies
async def get_user_profile_service_with_db(
    session: AsyncSession = Depends(get_db_session),
    service: UserProfileService = Depends(get_user_profile_service)
) -> tuple[UserProfileService, AsyncSession]:
    """Get service with database session."""
    return service, session
