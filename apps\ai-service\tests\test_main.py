"""
Basic tests for the EVOLVE AI Service main application.
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_root_endpoint():
    """Test the root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "EVOLVE AI Service"
    assert "version" in data

def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "EVOLVE AI Service"

def test_api_health_check():
    """Test the API health check endpoint."""
    response = client.get("/api/v1/health/")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_api_capabilities():
    """Test the AI capabilities endpoint."""
    response = client.get("/api/v1/ai/capabilities")
    assert response.status_code == 200
    data = response.json()
    assert "capabilities" in data
    assert "integrations" in data
