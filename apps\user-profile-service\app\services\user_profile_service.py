"""
User Profile Service for EVOLVE User Profile Service
"""

import logging
from typing import Dict, Optional, Tu<PERSON>, List
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas import UserProfileCreate, UserProfileUpdate, UserProfileRead
from app.models import UserProfile
from app.repositories import user_profile_repository

logger = logging.getLogger(__name__)


class UserProfileService:
    """User Profile service for handling business logic and database operations"""
    
    def __init__(self):
        """Initialize the service"""
        pass
    
    async def create_user_profile(self, session: AsyncSession, profile_data: UserProfileCreate) -> Tuple[Optional[UserProfileRead], Optional[str]]:
        """
        Create a new user profile
        
        Args:
            session: Database session
            profile_data: User profile creation data
            
        Returns:
            Tuple of (profile_data, error_message)
        """
        try:
            # Check if profile already exists
            existing_profile = await user_profile_repository.get_user_profile(session, profile_data.user_id)
            if existing_profile:
                return None, f"Profile for user {profile_data.user_id} already exists"
            
            # Create UserProfile model instance
            user_profile = UserProfile(
                user_id=profile_data.user_id,
                full_name=profile_data.full_name,
                avatar_url=profile_data.avatar_url,
                bio=profile_data.bio,
                university_name=profile_data.university_name,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Insert into database
            created_profile = await user_profile_repository.create_user_profile(session, user_profile)
            
            if created_profile:
                profile_read = UserProfileRead.model_validate(created_profile)
                return profile_read, None
            else:
                return None, "Failed to create user profile"
                
        except Exception as e:
            logger.error(f"Unexpected error creating profile: {e}")
            return None, "Profile creation service error"
    
    async def get_user_profile(self, session: AsyncSession, user_id: str) -> Tuple[Optional[UserProfileRead], Optional[str]]:
        """
        Get user profile by user ID
        
        Args:
            session: Database session
            user_id: User ID to retrieve profile for
            
        Returns:
            Tuple of (profile_data, error_message)
        """
        try:
            profile = await user_profile_repository.get_user_profile(session, user_id)
            
            if profile:
                profile_read = UserProfileRead.model_validate(profile)
                return profile_read, None
            else:
                return None, "Profile not found"
                
        except Exception as e:
            logger.error(f"Unexpected error getting profile: {e}")
            return None, "Profile retrieval service error"
    
    async def update_user_profile(self, session: AsyncSession, user_id: str, profile_data: UserProfileUpdate) -> Tuple[Optional[UserProfileRead], Optional[str]]:
        """
        Update user profile
        
        Args:
            session: Database session
            user_id: User ID to update profile for
            profile_data: Updated profile data
            
        Returns:
            Tuple of (updated_profile_data, error_message)
        """
        try:
            # Prepare update data (only include non-None values)
            update_dict = {}
            for field, value in profile_data.model_dump().items():
                if value is not None:
                    update_dict[field] = value
            
            if not update_dict:
                # No changes to make, return current profile
                return await self.get_user_profile(session, user_id)
            
            # Update in database
            updated_profile = await user_profile_repository.update_user_profile(session, user_id, update_dict)
            
            if updated_profile:
                profile_read = UserProfileRead.model_validate(updated_profile)
                return profile_read, None
            else:
                return None, "Profile not found or failed to update"
                
        except Exception as e:
            logger.error(f"Unexpected error updating profile: {e}")
            return None, "Profile update service error"
    
    async def delete_user_profile(self, session: AsyncSession, user_id: str) -> Tuple[bool, Optional[str]]:
        """
        Delete user profile
        
        Args:
            session: Database session
            user_id: User ID to delete profile for
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Delete from database
            success = await user_profile_repository.delete_user_profile(session, user_id)
            
            if success:
                return True, None
            else:
                return False, "Profile not found"
                
        except Exception as e:
            logger.error(f"Unexpected error deleting profile: {e}")
            return False, "Profile deletion service error"
    
    async def list_user_profiles(self, session: AsyncSession, skip: int = 0, limit: int = 100) -> Tuple[List[UserProfileRead], Optional[str]]:
        """
        List user profiles with pagination
        
        Args:
            session: Database session
            skip: Number of profiles to skip
            limit: Maximum number of profiles to return
            
        Returns:
            Tuple of (profiles_list, error_message)
        """
        try:
            profiles = await user_profile_repository.get_user_profiles(session, skip, limit)
            
            if profiles:
                profiles_read = [UserProfileRead.model_validate(profile) for profile in profiles]
                return profiles_read, None
            else:
                return [], None
                
        except Exception as e:
            logger.error(f"Unexpected error listing profiles: {e}")
            return [], "Profile listing service error"
    


# Function to create service instance
def create_user_profile_service() -> UserProfileService:
    """Create a UserProfileService instance"""
    return UserProfileService()
