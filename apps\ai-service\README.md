# EVOLVE AI Service

The AI service for the EVOLVE platform, built with FastAPI and integrating LangChain/LangGraph for advanced AI capabilities.

## Features

- **Conversational AI**: Multi-turn conversations with context awareness
- **RAG Pipeline**: Retrieval-Augmented Generation for contextual responses
- **Teaching Modes**: Adaptive AI responses based on pedagogical approaches
- **Multi-LLM Support**: OpenAI GPT-4, Google Gemini, and other models
- **LangChain Integration**: Advanced AI workflows and orchestration
- **RESTful API**: Well-documented endpoints with automatic OpenAPI generation

## Technology Stack

- **FastAPI**: Modern, fast web framework for building APIs
- **Pydantic v2**: Data validation and settings management
- **LangChain/LangGraph**: AI/ML orchestration and workflows
- **SQLModel**: SQL databases and ORM
- **Uvicorn**: ASGI server for serving the application
- **Python 3.12**: Latest Python features and performance improvements

## Quick Start

### Prerequisites

- Python 3.12+
- pip or conda for package management

### Installation

1. **Create and activate virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the application:**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Using the Application

1. **Access the API documentation:**
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

2. **Health check:**
   ```bash
   curl http://localhost:8000/health
   ```

3. **Test AI capabilities:**
   ```bash
   curl http://localhost:8000/api/v1/ai/capabilities
   ```

## API Endpoints

### Core Endpoints

- `GET /health` - Health check
- `GET /` - Service information
- `GET /api/v1/health/` - Detailed health check

### AI Endpoints

- `POST /api/v1/ai/chat` - Conversational AI
- `POST /api/v1/ai/rag` - Retrieval-Augmented Generation
- `POST /api/v1/ai/suggestions` - Contextual suggestions
- `GET /api/v1/ai/models` - Available AI models
- `GET /api/v1/ai/capabilities` - AI service capabilities

## Configuration

The application uses environment variables for configuration. Key settings include:

- `ENVIRONMENT`: Application environment (development/production)
- `HOST`: Host address (default: 127.0.0.1)
- `PORT`: Port number (default: 8000)
- `OPENAI_API_KEY`: OpenAI API key for GPT models
- `GOOGLE_AI_API_KEY`: Google AI API key for Gemini models

## Development

### Running Tests

```bash
pytest tests/
```

### Code Style

The project uses:
- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking

### Project Structure

```
app/
├── main.py                 # FastAPI application entry point
├── core/                   # Core application modules
│   ├── config.py          # Configuration settings
│   ├── logging.py         # Logging setup
│   └── exceptions.py      # Custom exceptions
├── api/                    # API route definitions
│   └── v1/                # API version 1
│       ├── router.py      # Main router
│       └── endpoints/     # Individual endpoint modules
├── services/              # Business logic services
│   └── ai_service.py      # AI service implementation
├── schemas/               # Pydantic models
│   └── ai_schemas.py      # AI-related schemas
├── models/                # Database models (future)
├── db/                    # Database configuration (future)
└── utils/                 # Utility functions
```

## Teaching Modes

The AI service supports multiple teaching modes:

- **Spoon-feed**: Detailed explanations with step-by-step guidance
- **Questioner**: Socratic questioning to guide learning
- **Socratic**: Classical Socratic method implementation
- **Collaborative**: Cooperative learning approach
- **Explorative**: Open-ended exploration of topics

## LangChain Integration

The service leverages LangChain for:

- **LLM Orchestration**: Managing multiple AI models
- **Memory Management**: Conversation context retention
- **Prompt Engineering**: Optimized prompts for different use cases
- **RAG Pipelines**: Retrieval-Augmented Generation workflows
- **Agent Workflows**: Complex AI task execution

## Error Handling

The application includes comprehensive error handling:

- **Custom Exceptions**: Domain-specific error types
- **Global Exception Handler**: Centralized error processing
- **Structured Logging**: Detailed error logging for debugging
- **API Error Responses**: Consistent error response format

## Security

Security features include:

- **CORS Configuration**: Cross-origin request handling
- **Trusted Hosts**: Host validation middleware
- **Input Validation**: Pydantic model validation
- **Rate Limiting**: API request throttling (future)
- **Authentication**: JWT-based authentication (future)

## Performance

Performance optimizations:

- **Async/Await**: Asynchronous request handling
- **Connection Pooling**: Database connection management
- **Caching**: Response caching for improved latency
- **Token Management**: Efficient LLM token usage

## Deployment

The service is designed for various deployment scenarios:

- **Local Development**: Direct Python execution
- **Docker**: Containerized deployment
- **Cloud Platforms**: AWS, GCP, Azure compatible
- **Kubernetes**: Container orchestration ready

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is part of the EVOLVE platform and follows the main project's licensing terms.

## Support

For questions and support:
- Check the API documentation at `/docs`
- Review the test examples in `tests/`
- Contact the development team

---

**EVOLVE AI Service** - Empowering education through intelligent AI assistance.
