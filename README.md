# EVOLVE - AI-Powered Learning Platform

EVOLVE is a comprehensive AI-powered learning platform designed to revolutionize the educational experience for university students. Built as a monorepo with modern web technologies, it provides personalized tutoring, intelligent content management, and interactive learning tools.

## 🔐 Authentication System

EVOLVE features a production-ready, secure authentication system built with FastAPI and Supabase, providing comprehensive user management with JWT token handling, OAuth integration, and enterprise-grade security.

## 🏗️ Project Structure

This monorepo is organized using Turborepo and follows a microservices architecture:

```
EVOLVE/
├── apps/                          # Application services
│   ├── frontend/                  # Next.js frontend application
│   ├── api-gateway/              # TypeScript/Node.js API Gateway
│   ├── ai-service/               # Python/FastAPI AI service
│   ├── auth-service/             # Authentication microservice
│   ├── content-service/          # Content management service
│   └── user-profile-service/     # User profile management
├── packages/                      # Shared packages
│   ├── ui-components/            # Shared UI components (shadcn/ui)
│   ├── shared-types/             # TypeScript type definitions
│   ├── utils/                    # Shared utility functions
│   └── hooks/                    # Shared React hooks
├── scripts/                       # Build and deployment scripts
├── docs/                         # Project documentation
├── .gitignore                    # Git ignore patterns
└── README.md                     # This file
```

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **State Management**: React Context API / Zustand

### Backend Services
- **API Gateway**: TypeScript/Node.js 18+
- **AI Service**: Python 3.12 + FastAPI
- **Auth Service**: Python FastAPI + Supabase + JWT
- **Content Service**: Content management and delivery
- **User Profile Service**: User data and preferences
- **Notification Service**: Real-time notifications
- **Database**: PostgreSQL (Supabase) with PGVector
- **Knowledge Graph**: Neo4j Aura
- **Search**: Typesense
- **Caching**: Redis
- **Storage**: MinIO (S3-compatible)

### AI & ML
- **LLM Integration**: OpenAI GPT-4, Google Gemini
- **AI Orchestration**: LangChain/LangGraph
- **Embeddings**: Sentence Transformers
- **RAG Pipeline**: Custom implementation with vector search

### Development Tools
- **Monorepo**: Turborepo
- **Package Manager**: pnpm
- **Testing**: Jest, Cypress
- **CI/CD**: GitHub Actions
- **Containerization**: Docker

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Python 3.12+
- pnpm
- Docker & Docker Compose
- Git

### Development Setup
```bash
# Clone the repository
git clone <repository-url>
cd EVOLVE

# Copy environment variables
cp .env.example .env

# Install dependencies
pnpm install

# Start development services
docker compose up -d

# Run all services in development mode
pnpm dev
```

This will start:
- Frontend at `http://localhost:3000`
- API Gateway at `http://localhost:8000`
- Auth Service at `http://localhost:8001`
- AI Service at `http://localhost:8002`
- Content Service at `http://localhost:8003`
- User Profile Service at `http://localhost:8004`
- Notification Service at `http://localhost:8005`
- PostgreSQL, Redis, MinIO, and Typesense via Docker

## 📚 Architecture Overview

EVOLVE follows a microservices architecture with the following key principles:

### Core Services
1. **Frontend (Next.js)**: Server-side rendered React application with the App Router
2. **API Gateway (Node.js)**: Central entry point for frontend requests with authentication middleware
3. **Auth Service (Python/FastAPI)**: JWT-based authentication with Supabase integration
4. **AI Service (Python/FastAPI)**: Handles all AI/ML operations and RAG pipelines
5. **Content Service**: Handles course content and knowledge management
6. **User Profile Service**: Manages user data and preferences
7. **Notification Service**: Real-time notifications and messaging

### Data Layer
- **PostgreSQL**: Primary database for structured data
- **Neo4j**: Knowledge graph for concept relationships
- **Redis**: Caching and real-time features
- **MinIO**: Object storage for files and media
- **Typesense**: Full-text search capabilities

### AI Features
- **RAG Pipeline**: Retrieval-Augmented Generation for contextual responses
- **Knowledge Graph**: Multi-hop concept browsing and mind maps
- **Personalized Tutoring**: Adaptive AI tutoring based on learning patterns
- **Content Generation**: AI-powered course content and explanations

## 🎨 Design System

EVOLVE features a unique "paper, black and white contrast, line art style" design identity:

- **Color Palette**: Primary black and white with blue (#007BFF), green (#28A745), and yellow (#FFC107) accents
- **Typography**: Clean sans-serif for body text, elegant cursive for headings
- **Visual Elements**: Hand-drawn line art illustrations and icons
- **Components**: Built on shadcn/ui with custom theming

## 🔒 Security & Privacy

- **Row-Level Security (RLS)**: Implemented in Supabase for data isolation
- **OAuth Integration**: Limited scope integrations with external services
- **GDPR & COPPA Compliance**: No data collection for users under 13
- **API Security**: JWT authentication with nightly key rotation

## 📊 Development Workflow

### Git Workflow
- **Main Branch**: Production-ready code
- **Feature Branches**: `feature/description`
- **Pull Requests**: Required for all changes
- **Conventional Commits**: Standardized commit messages

### CI/CD Pipeline
- **Linting**: ESLint, Prettier, Black
- **Testing**: Jest (unit), Cypress (E2E)
- **Build**: Automated builds on PR
- **Deployment**: Staging and production environments

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For questions and support, please reach out to the development team or create an issue in the repository.

---

**EVOLVE** - Empowering learning through AI-driven personalization.
