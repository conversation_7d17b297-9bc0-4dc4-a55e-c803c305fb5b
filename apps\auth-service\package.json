{"name": "@evolve/auth-service", "version": "1.0.0", "description": "EVOLVE Auth Service - FastAPI authentication and authorization service", "private": true, "scripts": {"dev": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload", "start": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8001", "auth:dev": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload", "auth:start": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8001", "build": "echo 'Auth Service build complete'", "auth:build": "echo 'Auth Service build complete'", "test": "python -m pytest", "auth:test": "python -m pytest", "lint": "python -m flake8 app/", "auth:lint": "python -m flake8 app/", "format": "python -m black app/", "format:check": "python -m black --check app/", "type-check": "python -m mypy app/", "install": "pip install -r requirements.txt"}, "keywords": ["<PERSON><PERSON><PERSON>", "authentication", "authorization", "backend", "python"], "author": "", "license": "ISC"}