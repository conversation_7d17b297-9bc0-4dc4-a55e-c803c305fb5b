"""
JWT utilities for EVOLVE Auth Service
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext

from app.core.config import settings

logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class JWTManager:
    """JWT token management utilities"""
    
    def __init__(self):
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    def create_access_token(self, subject: str, expires_delta: Optional[timedelta] = None) -> str:
        """
        Create a JWT access token
        
        Args:
            subject: Subject (usually user ID)
            expires_delta: Optional expiration time delta
            
        Returns:
            JWT access token string
        """
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode = {
            "exp": expire,
            "iat": datetime.utcnow(),
            "sub": str(subject),
            "type": "access"
        }
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(self, subject: str, expires_delta: Optional[timedelta] = None) -> str:
        """
        Create a JWT refresh token
        
        Args:
            subject: Subject (usually user ID)
            expires_delta: Optional expiration time delta
            
        Returns:
            JWT refresh token string
        """
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        
        to_encode = {
            "exp": expire,
            "iat": datetime.utcnow(),
            "sub": str(subject),
            "type": "refresh"
        }
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Verify and decode a JWT token
        
        Args:
            token: JWT token string
            
        Returns:
            Decoded token payload or None if invalid
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError as e:
            logger.error(f"JWT verification failed: {e}")
            return None
    
    def verify_access_token(self, token: str) -> Optional[str]:
        """
        Verify access token and return subject
        
        Args:
            token: Access token string
            
        Returns:
            Subject (user ID) or None if invalid
        """
        payload = self.verify_token(token)
        if payload and payload.get("type") == "access":
            return payload.get("sub")
        return None
    
    def verify_refresh_token(self, token: str) -> Optional[str]:
        """
        Verify refresh token and return subject
        
        Args:
            token: Refresh token string
            
        Returns:
            Subject (user ID) or None if invalid
        """
        payload = self.verify_token(token)
        if payload and payload.get("type") == "refresh":
            return payload.get("sub")
        return None
    
    def get_token_expiry(self, token: str) -> Optional[datetime]:
        """
        Get token expiry time
        
        Args:
            token: JWT token string
            
        Returns:
            Expiry datetime or None if invalid
        """
        payload = self.verify_token(token)
        if payload and payload.get("exp"):
            return datetime.fromtimestamp(payload["exp"])
        return None
    
    def is_token_expired(self, token: str) -> bool:
        """
        Check if token is expired
        
        Args:
            token: JWT token string
            
        Returns:
            True if expired, False otherwise
        """
        expiry = self.get_token_expiry(token)
        if expiry:
            return datetime.utcnow() > expiry
        return True
    
    def hash_password(self, password: str) -> str:
        """
        Hash a password
        
        Args:
            password: Plain text password
            
        Returns:
            Hashed password
        """
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify a password against its hash
        
        Args:
            plain_password: Plain text password
            hashed_password: Hashed password
            
        Returns:
            True if password matches, False otherwise
        """
        return pwd_context.verify(plain_password, hashed_password)


# Global JWT manager instance
jwt_manager = JWTManager()
