"""
AI Service Endpoints

This module provides AI-related endpoints for the EVOLVE platform, including
LangChain/LangGraph operations, RAG pipelines, and AI model interactions.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.core.exceptions import AIServiceError
from app.services.ai_service import AIService
from app.schemas.ai_schemas import (
    ChatRequest, 
    ChatResponse, 
    RAGRequest, 
    RAGResponse,
    SuggestionRequest,
    SuggestionResponse
)

router = APIRouter()

# Dependency to get AI service instance
def get_ai_service() -> AIService:
    """
    Dependency to get AI service instance.
    """
    return AIService()

@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai(
    request: ChatRequest,
    ai_service: AIService = Depends(get_ai_service)
) -> ChatResponse:
    """
    Chat with AI using Lang<PERSON>hain/LangGraph.
    
    This endpoint provides conversational AI capabilities using the configured
    LLM models (OpenAI GPT-4, Google Gemini, etc.).
    
    Args:
        request: Chat request containing message and context
        ai_service: Injected AI service instance
        
    Returns:
        ChatResponse containing AI's response
    """
    try:
        response = await ai_service.chat(request)
        return response
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"AI chat failed: {str(e)}"
        )

@router.post("/rag", response_model=RAGResponse)
async def retrieval_augmented_generation(
    request: RAGRequest,
    ai_service: AIService = Depends(get_ai_service)
) -> RAGResponse:
    """
    Perform Retrieval-Augmented Generation (RAG) for contextual AI responses.
    
    This endpoint retrieves relevant information from the knowledge base
    and generates AI responses based on that context.
    
    Args:
        request: RAG request containing query and optional filters
        ai_service: Injected AI service instance
        
    Returns:
        RAGResponse containing AI response with sources
    """
    try:
        response = await ai_service.rag_query(request)
        return response
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"RAG query failed: {str(e)}"
        )

@router.post("/suggestions", response_model=SuggestionResponse)
async def get_context_suggestions(
    request: SuggestionRequest,
    ai_service: AIService = Depends(get_ai_service)
) -> SuggestionResponse:
    """
    Get contextual suggestions for user queries.
    
    This endpoint analyzes user context and provides intelligent suggestions
    for teaching modes, query refinements, and learning paths.
    
    Args:
        request: Suggestion request containing context and user preferences
        ai_service: Injected AI service instance
        
    Returns:
        SuggestionResponse containing contextual suggestions
    """
    try:
        response = await ai_service.get_suggestions(request)
        return response
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Suggestion generation failed: {str(e)}"
        )

@router.get("/models")
async def get_available_models() -> Dict[str, Any]:
    """
    Get information about available AI models.
    
    Returns:
        Dict containing available models and their capabilities
    """
    return {
        "models": {
            "gpt-4": {
                "provider": "OpenAI",
                "capabilities": ["chat", "reasoning", "code_generation"],
                "status": "available"
            },
            "gpt-3.5-turbo": {
                "provider": "OpenAI", 
                "capabilities": ["chat", "summarization"],
                "status": "available"
            },
            "gemini-pro": {
                "provider": "Google AI",
                "capabilities": ["chat", "reasoning", "multimodal"],
                "status": "available"
            }
        },
        "default_model": "gpt-4",
        "timestamp": datetime.utcnow().isoformat()
    }

@router.get("/capabilities")
async def get_ai_capabilities() -> Dict[str, Any]:
    """
    Get information about AI service capabilities.
    
    Returns:
        Dict containing available AI capabilities and features
    """
    return {
        "capabilities": {
            "chat": {
                "description": "Conversational AI with context awareness",
                "features": ["multi-turn conversations", "context retention", "personality adaptation"]
            },
            "rag": {
                "description": "Retrieval-Augmented Generation for contextual responses",
                "features": ["knowledge base search", "source attribution", "semantic similarity"]
            },
            "suggestions": {
                "description": "Contextual suggestions and recommendations",
                "features": ["teaching mode suggestions", "query refinement", "learning path recommendations"]
            },
            "knowledge_graph": {
                "description": "Knowledge graph operations and visualization",
                "features": ["concept mapping", "relationship discovery", "mind map generation"]
            }
        },
        "integrations": {
            "langchain": "0.3.26",
            "langgraph": "0.5.2",
            "openai": "1.95.0",
            "google-ai": "2.1.7"
        },
        "timestamp": datetime.utcnow().isoformat()
    }
