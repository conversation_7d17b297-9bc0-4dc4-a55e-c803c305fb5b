"""
EVOLVE AI Service - FastAPI Application Entry Point

This is the main entry point for the EVOLVE AI Service, handling AI/ML operations,
LangChain/LangGraph workflows, and data processing for the EVOLVE platform.

Architecture:
- FastAPI application with async/await support
- Pydantic v2 for data validation
- <PERSON><PERSON><PERSON><PERSON>/LangGraph for AI orchestration
- SQLModel for database operations
- Modular API routes under /api/v1
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.v1.router import api_router
from app.core.exceptions import EvolveException

# Setup logging
setup_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.
    """
    # Startup
    print(f"Starting EVOLVE AI Service on {settings.HOST}:{settings.PORT}")
    print(f"Environment: {settings.ENVIRONMENT}")
    
    # Initialize services here (database connections, AI models, etc.)
    
    yield
    
    # Shutdown
    print("Shutting down EVOLVE AI Service")
    # Cleanup resources here


# Create FastAPI application instance
app = FastAPI(
    title="EVOLVE AI Service",
    description="AI-powered backend service for the EVOLVE learning platform",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add trusted host middleware for security
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.TRUSTED_HOSTS
)

# Global exception handler
@app.exception_handler(EvolveException)
async def evolve_exception_handler(request, exc: EvolveException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.message, "details": exc.details}
    )

@app.exception_handler(Exception)
async def global_exception_handler(request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "details": str(exc)}
    )

# Health check endpoint
@app.get("/health")
async def health_check():
    """
    Health check endpoint for monitoring and load balancers.
    """
    return {
        "status": "healthy",
        "service": "EVOLVE AI Service",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# Root endpoint
@app.get("/")
async def root():
    """
    Root endpoint providing service information.
    """
    return {
        "message": "EVOLVE AI Service",
        "version": "1.0.0",
        "docs": "/docs" if settings.ENVIRONMENT == "development" else "Not available in production",
        "health": "/health"
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.ENVIRONMENT == "development",
        log_level="info"
    )
