import * as React from "react"
import { Button, buttonVariants } from "@/app/components/ui/button"
import { cn } from "@/app/lib/utils"
import { type VariantProps } from "class-variance-authority"

interface SketchButtonProps
  extends React.ComponentProps<typeof Button>,
    VariantProps<typeof buttonVariants> {
  sketch?: boolean
}

/**
 * SketchButton - A hand-drawn style button component for EVOLVE
 * Extends the shadcn/ui Button with custom sketch styling
 */
export function SketchButton({
  className,
  sketch = true,
  variant = "outline",
  ...props
}: SketchButtonProps) {
  return (
    <Button
      className={cn(
        sketch && [
          "sketch-button",
          "scribble-hover",
          "font-medium",
          "border-2",
          "border-current",
          "bg-transparent",
          "hover:bg-current/5",
          "focus:bg-current/5",
          "active:bg-current/10",
          "transition-all",
          "duration-200",
          "ease-in-out",
        ],
        className
      )}
      variant={variant}
      {...props}
    />
  )
}

SketchButton.displayName = "SketchButton"
