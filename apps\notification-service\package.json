{"name": "@evolve/notification-service", "version": "1.0.0", "private": true, "scripts": {"build": "echo \"Building @evolve/notification-service... (placeholder)\"", "dev": "echo \"Starting @evolve/notification-service... (placeholder for ts-node/nodemon)\"", "start": "echo \"Running @evolve/notification-service... (placeholder)\"", "lint": "echo \"Linting @evolve/notification-service... (placeholder)\"", "test": "echo \"Testing @evolve/notification-service... (placeholder)\""}, "dependencies": {"express": "^4.19.2", "ts-node": "^10.9.2", "typescript": "^5.5.3", "@types/express": "^4.17.21", "@types/node": "^20.14.10"}}