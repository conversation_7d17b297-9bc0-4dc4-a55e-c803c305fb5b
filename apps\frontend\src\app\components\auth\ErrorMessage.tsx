import * as React from "react"
import { cn } from "@/app/lib/utils"
import { Alert, AlertDescription } from "@/app/components/ui/alert"

interface ErrorMessageProps {
  message?: string
  className?: string
  variant?: "default" | "destructive"
  onRetry?: () => void
  retryLabel?: string
  showRetry?: boolean
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  className,
  variant = "destructive",
  onRetry,
  retryLabel = "Try again",
  showRetry = false
}) => {
  if (!message) return null

  return (
    <Alert variant={variant} className={cn("animate-in fade-in-50", className)}>
      <AlertDescription>
        <div className="space-y-2">
          <p className="text-sm">{message}</p>
          
          {showRetry && onRetry && (
            <div className="pt-2 border-t border-red-200">
              <button
                onClick={onRetry}
                className="text-sm px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded transition-colors"
              >
                {retryLabel}
              </button>
            </div>
          )}
        </div>
      </AlertDescription>
    </Alert>
  )
}

export { ErrorMessage }
