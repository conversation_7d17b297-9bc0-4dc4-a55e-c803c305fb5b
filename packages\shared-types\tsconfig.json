{"extends": "../../tsconfig.json", "compilerOptions": {"declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "lib": ["es2020"], "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.*", "**/*.spec.*"]}