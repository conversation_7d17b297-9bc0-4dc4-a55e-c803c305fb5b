# EVOLVE Development Nginx Configuration
# This configuration routes requests to appropriate services

# Upstream definitions
upstream frontend {
    server host.docker.internal:3000;
}

upstream api {
    server host.docker.internal:3001;
}

upstream minio {
    server minio:9000;
}

upstream minio-console {
    server minio:9001;
}

# Main server block
server {
    listen 80;
    server_name localhost;

    # Frontend application
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # API routes
    location /api/ {
        proxy_pass http://api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Increase body size for file uploads
        client_max_body_size 10M;
    }

    # MinIO storage
    location /storage/ {
        proxy_pass http://minio/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Remove storage prefix
        rewrite ^/storage/(.*)$ /$1 break;
    }

    # MinIO console
    location /minio-console/ {
        proxy_pass http://minio-console/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Remove minio-console prefix
        rewrite ^/minio-console/(.*)$ /$1 break;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "OK";
        add_header Content-Type text/plain;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
}
