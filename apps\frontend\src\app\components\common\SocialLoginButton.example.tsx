/**
 * Example usage of the enhanced SocialLoginButton component
 * This file demonstrates how to properly use the component with error handling
 */

"use client"

import React, { useState } from "react"
import { SocialLoginButton } from "./SocialLoginButton"

export function SocialLoginExample() {
  const [globalError, setGlobalError] = useState<string | null>(null)
  const [isAnyLoading, setIsAnyLoading] = useState(false)

  const handleError = (error: string) => {
    setGlobalError(error)
    
    // Optional: Clear error after some time
    setTimeout(() => {
      setGlobalError(null)
    }, 8000)
  }

  const handleLoadingChange = (loading: boolean) => {
    setIsAnyLoading(loading)
  }

  return (
    <div className="space-y-4 max-w-md mx-auto">
      <h2 className="text-xl font-heading text-center">Social Login</h2>
      
      {/* Global error display (optional, since each button shows its own errors) */}
      {globalError && (
        <div className="p-3 bg-red-100 border border-red-300 rounded-md">
          <p className="text-sm text-red-700">{globalError}</p>
        </div>
      )}
      
      {/* Social login buttons */}
      <div className="space-y-3">
        <SocialLoginButton
          provider="google"
          mode="login"
          onError={handleError}
          onLoadingChange={handleLoadingChange}
          disabled={isAnyLoading} // Prevent multiple simultaneous auth attempts
        />
        
        <SocialLoginButton
          provider="github"
          mode="login"
          onError={handleError}
          onLoadingChange={handleLoadingChange}
          disabled={isAnyLoading} // Prevent multiple simultaneous auth attempts
        />
      </div>
      
      {/* Optional: Show global loading state */}
      {isAnyLoading && (
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Authenticating... Please do not close this window.
          </p>
        </div>
      )}
      
      {/* Optional: Instructions for users */}
      <div className="text-center text-sm text-muted-foreground">
        <p>
          By continuing, you agree to our Terms of Service and Privacy Policy.
        </p>
      </div>
    </div>
  )
}

export default SocialLoginExample
