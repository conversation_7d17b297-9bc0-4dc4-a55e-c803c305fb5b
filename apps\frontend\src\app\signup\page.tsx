"use client"

import React, { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useAuth } from "@/app/hooks/useAuth"
import { useAuthToast } from "@/app/components/ui/toast-provider"

import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { SketchButton } from "@/app/components/common/SketchButton"
import { SocialLoginButton } from "@/app/components/common/SocialLoginButton"
import { PaperCard, PaperCardHeader, PaperCardTitle, PaperCardContent } from "@/app/components/common/PaperCard"
import { ErrorMessage } from "@/app/components/auth/ErrorMessage"
import { PasswordStrengthIndicator } from "@/app/components/auth/PasswordStrengthIndicator"
import { Logo } from "@/app/components/common/Logo"

interface SignupFormData {
  email: string
  password: string
  confirmPassword: string
  fullName: string
}

export default function SignupPage() {
  const router = useRouter()
  const auth = useAuth({ useSupabase: true, showToasts: true })
  const toast = useAuthToast()
  const [isNavigating, setIsNavigating] = useState(false)

  const navigateTo = useCallback((path: string) => {
    setIsNavigating(true)
    router.push(path)
  }, [router])
  
  const [formData, setFormData] = useState<SignupFormData>({
    email: "",
    password: "",
    confirmPassword: "",
    fullName: "",
  })
  const [errors, setErrors] = useState<Partial<SignupFormData>>({})
  const [showRetryInfo, setShowRetryInfo] = useState(false)
  const [emailConfirmationPending, setEmailConfirmationPending] = useState(false)

  const validateForm = (): boolean => {
    const newErrors: Partial<SignupFormData> = {}

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required"
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters"
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password"
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match"
    }

    // Full name validation
    if (!formData.fullName) {
      newErrors.fullName = "Full name is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error for this field when user starts typing
    if (errors[name as keyof SignupFormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }))
    }
  }

  const handleSignup = async (e: React.FormEvent) => {
    setEmailConfirmationPending(false)
    e.preventDefault()
    
    console.log('Signup form submitted', { formData })
    
    if (!validateForm()) {
      console.log('Form validation failed', errors)
      return
    }

    console.log('Form validation passed')

    if (auth.isAuthenticated) {
      console.log('User already authenticated, redirecting to dashboard')
      navigateTo('/dashboard')
      return
    }

    console.log('Starting signup process...')
    
    // Clear any previous errors
    auth.clearError()
    setShowRetryInfo(false)

    try {
      console.log('Calling auth.signup with:', { email: formData.email, fullName: formData.fullName })
      const success = await auth.signup(formData.email, formData.password, formData.fullName)
      
      console.log('Signup result:', success)
      
      if (success) {
        console.log('Signup successful, processing result')
        
        // Store email before clearing form
        const userEmail = formData.email
        
        // Show success notification
        if (auth.isAuthenticated) {
          // User was immediately logged in, redirect to dashboard
          toast.showSuccess(
            'Your account has been created and you are now logged in.',
            'Account created successfully! Welcome to Evolve!'
          )
          
          // Clear form
          setFormData({
            email: "",
            password: "",
            confirmPassword: "",
            fullName: "",
          })
          
          setTimeout(() => {
            navigateTo('/dashboard')
          }, 1500)
        } else {
          // Email confirmation required
          const message = `A verification email has been sent to ${userEmail}. Please check your inbox and follow the instructions to verify your account.`
          
          toast.showSuccess(message)
          
          // Clear form after showing notification
          setFormData({
            email: "",
            password: "",
            confirmPassword: "",
            fullName: "",
          })
          
          // Show email confirmation pending state
          setEmailConfirmationPending(true)
          
          // Stay on the page and show the success message
          setIsNavigating(false)
          
          // Optionally redirect to login page after a delay
          setTimeout(() => {
            navigateTo('/login')
          }, 4000)
        }
      } else {
        console.log('Signup failed, checking for errors')
        if (auth.error) {
          console.log('Auth error:', auth.error)
          // Show retry information for retryable errors
          if (auth.error.retryable && auth.canRetry()) {
            setShowRetryInfo(true)
          }
        }
      }
    } catch (error) {
      console.error('Signup error caught:', error)
    }
  }
  
  const handleRetry = async () => {
    setShowRetryInfo(false)
    const success = await auth.retryLastOperation()
    
    if (success) {
      setFormData({ email: "", password: "", confirmPassword: "", fullName: "" })
      setTimeout(() => {
        navigateTo('/dashboard')
      }, 100)
    } else if (auth.error?.retryable && auth.canRetry()) {
      setShowRetryInfo(true)
    }
  }

useEffect(() => {
    return () => setIsNavigating(false)
  }, [])

  return (
    <div className="min-h-screen paper-bg flex items-center justify-center px-4 py-8">
      <div className="w-full max-w-md">
        <PaperCard className="shadow-xl">
          <PaperCardHeader className="text-center">
            <div className="mb-6">
              <Logo size="lg" showText={true} className="justify-center" />
            </div>
            <PaperCardTitle className="text-3xl font-heading mb-2">
              Join EVOLVE
            </PaperCardTitle>
            {emailConfirmationPending && (
              <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-green-800">Email Sent Successfully!</h4>
                    <p className="text-sm text-green-700">Please check your inbox and click the verification link to activate your account.</p>
                  </div>
                </div>
              </div>
            )}
            <p className="text-muted-foreground font-sans">
              Create your account to start your evolution journey
            </p>
          </PaperCardHeader>

          <PaperCardContent>
            {/* Error Display with Retry Options */}
            {auth.error && (
              <ErrorMessage 
                message={auth.error.userMessage} 
                showRetry={showRetryInfo && auth.canRetry()} 
                onRetry={handleRetry} 
                retryLabel={auth.isLoading ? 'Retrying...' : 'Retry Now'} 
                className="mb-6"
              />
            )}

            {/* Social Login Buttons */}
            <div className="space-y-3 mb-6">
              <SocialLoginButton 
                provider="google" 
                mode="signup"
                disabled={auth.isLoading}
                onError={(error) => {
                  toast.showError({
                    code: 'OAUTH_ERROR',
                    message: error,
                    userMessage: error,
                    retryable: true
                  } as any)
                }}
                onLoadingChange={() => {}}
              />
              <SocialLoginButton 
                provider="github" 
                mode="signup"
                disabled={auth.isLoading}
                onError={(error) => {
                  toast.showError({
                    code: 'OAUTH_ERROR',
                    message: error,
                    userMessage: error,
                    retryable: true
                  } as any)
                }}
                onLoadingChange={() => {}}
              />
            </div>

            {/* Divider */}
            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-dashed border-gray-300" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground font-sans">
                  Or continue with email
                </span>
              </div>
            </div>

            <form onSubmit={handleSignup} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="fullName">Full Name</Label>
                <Input
                  id="fullName"
                  name="fullName"
                  type="text"
                  placeholder="Enter your full name"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  disabled={auth.isLoading}
                  className={errors.fullName ? "border-destructive" : ""}
                />
                {errors.fullName && (
                  <p className="text-sm text-destructive">{errors.fullName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleInputChange}
                  disabled={auth.isLoading}
                  className={errors.email ? "border-destructive" : ""}
                />
                {errors.email && (
                  <p className="text-sm text-destructive">{errors.email}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Create a password (min 6 characters)"
                  value={formData.password}
                  onChange={handleInputChange}
                  disabled={auth.isLoading}
                  className={errors.password ? "border-destructive" : ""}
                />
                {errors.password && (
                  <p className="text-sm text-destructive">{errors.password}</p>
                )}
                {/* Password Strength Indicator */}
                {formData.password && (
                  <PasswordStrengthIndicator password={formData.password} />
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  placeholder="Confirm your password"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  disabled={auth.isLoading}
                  className={errors.confirmPassword ? "border-destructive" : ""}
                />
                {errors.confirmPassword && (
                  <p className="text-sm text-destructive">{errors.confirmPassword}</p>
                )}
              </div>

              <SketchButton
                type="submit"
                disabled={auth.isLoading || isNavigating}
                className="w-full py-3 text-lg font-medium"
                onClick={(e) => {
                  console.log('Button clicked', { isLoading: auth.isLoading, isNavigating })
                  // Let form onSubmit handle the actual submission
                }}
              >
                {isNavigating ? "Redirecting..." : auth.isLoading ? "Creating Account..." : "Create Account"}
              </SketchButton>
            </form>

            <div className="mt-6 text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                Already have an account?{" "}
<Link 
                  href="/login"
                  className="text-primary hover:underline font-medium scribble-hover"
                  onClick={(e) => isNavigating && e.preventDefault()}
                >
                  Sign in here
                </Link>
              </p>
              
              <div className="pt-4 border-t border-dashed border-gray-300">
                <p className="text-xs text-muted-foreground">
                  By creating an account, you agree to our{" "}
                  <Link href="/terms" className="underline hover:text-primary">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link href="/privacy" className="underline hover:text-primary">
                    Privacy Policy
                  </Link>
                </p>
              </div>
            </div>
          </PaperCardContent>
        </PaperCard>
      </div>
    </div>
  )
}
