version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: evolve-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-evolve}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - evolve-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-evolve}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MinIO (S3-compatible storage)
  minio:
    image: minio/minio:latest
    container_name: evolve-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-password123}
      MINIO_DEFAULT_BUCKETS: ${MINIO_DEFAULT_BUCKETS:-evolve-storage,evolve-avatars,evolve-documents}
    ports:
      - "${MINIO_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    volumes:
      - minio_data:/data
    networks:
      - evolve-network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Redis (Caching & Session Storage)
  redis:
    image: redis:7-alpine
    container_name: evolve-redis
    restart: unless-stopped
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - evolve-network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Typesense (Search Engine)
  typesense:
    image: typesense/typesense:0.25.2
    container_name: evolve-typesense
    restart: unless-stopped
    environment:
      TYPESENSE_API_KEY: ${TYPESENSE_API_KEY:-xyz}
      TYPESENSE_DATA_DIR: /data
    ports:
      - "${TYPESENSE_PORT:-8108}:8108"
    volumes:
      - typesense_data:/data
    networks:
      - evolve-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8108/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # LocalAI (Local LLM Inference)
  localai:
    image: quay.io/go-skynet/local-ai:latest
    container_name: evolve-localai
    restart: unless-stopped
    environment:
      CONTEXT_SIZE: ${LOCALAI_CONTEXT_SIZE:-512}
      MODELS_PATH: /models
      THREADS: ${LOCALAI_THREADS:-4}
      ADDRESS: 0.0.0.0:8080
      GALLERY_ENDPOINT: https://raw.githubusercontent.com/go-skynet/model-gallery/main/index.yaml
    ports:
      - "${LOCALAI_PORT:-8080}:8080"
    volumes:
      - localai_models:/models
    networks:
      - evolve-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/readyz"]
      interval: 60s
      timeout: 30s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Adminer (Database Management UI)
  adminer:
    image: adminer:latest
    container_name: evolve-adminer
    restart: unless-stopped
    ports:
      - "${ADMINER_PORT:-8081}:8080"
    networks:
      - evolve-network
    depends_on:
      - postgres
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: dracula

  # Redis Commander (Redis Management UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: evolve-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:${REDIS_PASSWORD:-redis123}
    ports:
      - "${REDIS_COMMANDER_PORT:-8082}:8081"
    networks:
      - evolve-network
    depends_on:
      - redis

  # Nginx (Reverse Proxy & Load Balancer)
  nginx:
    image: nginx:alpine
    container_name: evolve-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - evolve-network
    depends_on:
      - postgres
      - redis
      - minio
      - typesense

# Networks
networks:
  evolve-network:
    driver: bridge
    name: evolve-network
    ipam:
      driver: default
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
    name: evolve-postgres-data
  minio_data:
    driver: local
    name: evolve-minio-data
  redis_data:
    driver: local
    name: evolve-redis-data
  typesense_data:
    driver: local
    name: evolve-typesense-data
  localai_models:
    driver: local
    name: evolve-localai-models
