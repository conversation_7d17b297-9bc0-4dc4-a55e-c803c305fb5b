"""
Supabase service for EVOLVE Auth Service
"""

import logging
from typing import Dict, Optional, Tuple
from supabase import create_client, Client
from gotrue.errors import AuthApiError

from app.core.config import settings

logger = logging.getLogger(__name__)


class SupabaseService:
    """Supabase authentication service"""
    
    def __init__(self):
        """Initialize Supabase client"""
        self.supabase: Client = create_client(
            settings.SUPABASE_URL,
            settings.SUPABASE_ANON_KEY
        )
        
        # Service role client for admin operations
        self.supabase_admin: Client = create_client(
            settings.SUPABASE_URL,
            settings.SUPABASE_SERVICE_ROLE_KEY
        )
    
    async def sign_up(self, email: str, password: str, options: Optional[Dict] = None) -> Tuple[Optional[Dict], Optional[str]]:
        """
        Sign up a new user with email and password
        
        Args:
            email: User email
            password: User password
            options: Additional sign up options (metadata, etc.)
            
        Returns:
            Tuple of (user_data, error_message)
        """
        try:
            sign_up_data = {
                "email": email,
                "password": password
            }
            
            if options:
                sign_up_data["options"] = options
            
            response = self.supabase.auth.sign_up(sign_up_data)
            
            if response.user:
                user_data = {
                    "user": {
                        "id": response.user.id,
                        "email": response.user.email,
                        "email_verified": response.user.email_confirmed_at is not None,
                        "created_at": response.user.created_at,
                        "updated_at": response.user.updated_at,
                        "user_metadata": response.user.user_metadata,
                        "app_metadata": response.user.app_metadata
                    },
                    "session": None,
                    "confirmation_sent": not response.session  # If no session, confirmation email was sent
                }
                
                # If there's a session, user is automatically confirmed
                if response.session:
                    user_data["session"] = {
                        "access_token": response.session.access_token,
                        "refresh_token": response.session.refresh_token,
                        "expires_in": response.session.expires_in,
                        "expires_at": response.session.expires_at,
                        "token_type": response.session.token_type or "bearer"
                    }
                    user_data["confirmation_sent"] = False
                
                return user_data, None
            else:
                return None, "Sign up failed"
                
        except AuthApiError as e:
            logger.error(f"Supabase sign up error: {e}")
            return None, str(e)
        except Exception as e:
            logger.error(f"Unexpected error during sign up: {e}")
            return None, "Sign up service error"
    
    async def sign_in_with_password(self, email: str, password: str) -> Tuple[Optional[Dict], Optional[str]]:
        """
        Sign in user with email and password
        
        Args:
            email: User email
            password: User password
            
        Returns:
            Tuple of (user_data, error_message)
        """
        try:
            response = self.supabase.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            
            if response.user and response.session:
                user_data = {
                    "session": {
                        "access_token": response.session.access_token,
                        "refresh_token": response.session.refresh_token,
                        "expires_in": response.session.expires_in,
                        "expires_at": response.session.expires_at,
                        "token_type": response.session.token_type or "bearer"
                    },
                    "user": {
                        "id": response.user.id,
                        "email": response.user.email,
                        "email_verified": response.user.email_confirmed_at is not None,
                        "created_at": response.user.created_at,
                        "updated_at": response.user.updated_at,
                        "user_metadata": response.user.user_metadata,
                        "app_metadata": response.user.app_metadata
                    }
                }
                return user_data, None
            else:
                return None, "Authentication failed"
                
        except AuthApiError as e:
            logger.error(f"Supabase auth error: {e}")
            return None, str(e)
        except Exception as e:
            logger.error(f"Unexpected error during sign in: {e}")
            return None, "Authentication service error"
    
    async def get_oauth_url(self, provider: str, redirect_url: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Get OAuth authorization URL
        
        Args:
            provider: OAuth provider (google, github)
            redirect_url: Redirect URL after OAuth
            
        Returns:
            Tuple of (auth_url, error_message)
        """
        try:
            response = self.supabase.auth.sign_in_with_oauth({
                "provider": provider,
                "options": {
                    "redirect_to": redirect_url
                }
            })
            
            if response.url:
                return response.url, None
            else:
                return None, f"Failed to get OAuth URL for {provider}"
                
        except AuthApiError as e:
            logger.error(f"OAuth URL error: {e}")
            return None, str(e)
        except Exception as e:
            logger.error(f"Unexpected error getting OAuth URL: {e}")
            return None, "OAuth service error"
    
    async def refresh_token(self, refresh_token: str) -> Tuple[Optional[Dict], Optional[str]]:
        """
        Refresh access token
        
        Args:
            refresh_token: Refresh token
            
        Returns:
            Tuple of (token_data, error_message)
        """
        try:
            response = self.supabase.auth.refresh_session(refresh_token)
            
            if response.session:
                token_data = {
                    "access_token": response.session.access_token,
                    "refresh_token": response.session.refresh_token,
                    "expires_in": response.session.expires_in,
                    "token_type": response.session.token_type or "bearer"
                }
                return token_data, None
            else:
                return None, "Failed to refresh token"
                
        except AuthApiError as e:
            logger.error(f"Token refresh error: {e}")
            return None, str(e)
        except Exception as e:
            logger.error(f"Unexpected error during token refresh: {e}")
            return None, "Token refresh service error"
    
    async def sign_out(self, access_token: str) -> Tuple[bool, Optional[str]]:
        """
        Sign out user
        
        Args:
            access_token: Access token to invalidate
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Set the access token for this operation
            self.supabase.auth.set_session(access_token, None)
            
            response = self.supabase.auth.sign_out()
            return True, None
            
        except AuthApiError as e:
            logger.error(f"Sign out error: {e}")
            return False, str(e)
        except Exception as e:
            logger.error(f"Unexpected error during sign out: {e}")
            return False, "Sign out service error"
    
    async def get_user_info(self, access_token: str) -> Tuple[Optional[Dict], Optional[str]]:
        """
        Get user information from access token
        
        Args:
            access_token: Access token
            
        Returns:
            Tuple of (user_info, error_message)
        """
        try:
            # Set the access token for this operation
            self.supabase.auth.set_session(access_token, None)
            
            response = self.supabase.auth.get_user()
            
            if response.user:
                user_info = {
                    "id": response.user.id,
                    "email": response.user.email,
                    "email_verified": response.user.email_confirmed_at is not None,
                    "created_at": response.user.created_at,
                    "updated_at": response.user.updated_at,
                    "user_metadata": response.user.user_metadata,
                    "app_metadata": response.user.app_metadata
                }
                return user_info, None
            else:
                return None, "User not found"
                
        except AuthApiError as e:
            logger.error(f"Get user error: {e}")
            return None, str(e)
        except Exception as e:
            logger.error(f"Unexpected error getting user info: {e}")
            return None, "User service error"
    
    async def reset_password_for_email(self, email: str) -> Tuple[bool, Optional[str]]:
        """
        Send password reset email
        
        Args:
            email: User email address
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            response = self.supabase.auth.reset_password_for_email(email)
            return True, None
        except AuthApiError as e:
            logger.error(f"Password reset email error: {e}")
            return False, str(e)
        except Exception as e:
            logger.error(f"Unexpected error sending password reset email: {e}")
            return False, "Password reset service error"
    
    async def update_user_password(self, access_token: str, new_password: str) -> Tuple[Optional[Dict], Optional[str]]:
        """
        Update user password
        
        Args:
            access_token: Access token from password reset link
            new_password: New password
            
        Returns:
            Tuple of (user_data, error_message)
        """
        try:
            # Set the access token for this operation
            self.supabase.auth.set_session(access_token, None)
            
            response = self.supabase.auth.update_user({"password": new_password})
            
            if response.user:
                user_data = {
                    "id": response.user.id,
                    "email": response.user.email,
                    "email_verified": response.user.email_confirmed_at is not None,
                    "created_at": response.user.created_at,
                    "updated_at": response.user.updated_at,
                    "user_metadata": response.user.user_metadata,
                    "app_metadata": response.user.app_metadata
                }
                return user_data, None
            else:
                return None, "Failed to update password"
                
        except AuthApiError as e:
            logger.error(f"Password update error: {e}")
            return None, str(e)
        except Exception as e:
            logger.error(f"Unexpected error updating password: {e}")
            return None, "Password update service error"
    
    async def verify_email_with_token(self, access_token: str, refresh_token: str) -> Tuple[Optional[Dict], Optional[str]]:
        """
        Verify email with tokens from email verification link
        
        Args:
            access_token: Access token from verification email
            refresh_token: Refresh token from verification email
            
        Returns:
            Tuple of (user_data, error_message)
        """
        try:
            # Set the session with both tokens
            self.supabase.auth.set_session(access_token, refresh_token)
            
            # Get user info to verify the session
            response = self.supabase.auth.get_user()
            
            if response.user:
                user_data = {
                    "id": response.user.id,
                    "email": response.user.email,
                    "email_verified": response.user.email_confirmed_at is not None,
                    "created_at": response.user.created_at,
                    "updated_at": response.user.updated_at,
                    "user_metadata": response.user.user_metadata,
                    "app_metadata": response.user.app_metadata
                }
                return user_data, None
            else:
                return None, "Email verification failed"
                
        except AuthApiError as e:
            logger.error(f"Email verification error: {e}")
            return None, str(e)
        except Exception as e:
            logger.error(f"Unexpected error during email verification: {e}")
            return None, "Email verification service error"


# Global instance
supabase_service = SupabaseService()
