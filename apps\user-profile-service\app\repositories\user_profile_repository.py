"""
User Profile Repository - Async Database Operations
"""

from app.models import UserProfile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select
from typing import Optional, List
from datetime import datetime


async def get_user_profile(session: AsyncSession, user_id: str) -> Optional[UserProfile]:
    """
    Retrieve a user profile by user ID.
    """
    statement = select(UserProfile).where(UserProfile.user_id == user_id)
    result = await session.exec(statement)
    return result.first()


async def create_user_profile(session: AsyncSession, user_profile: UserProfile) -> UserProfile:
    """
    Create a new user profile.
    """
    session.add(user_profile)
    await session.commit()
    await session.refresh(user_profile)
    return user_profile


async def update_user_profile(session: AsyncSession, user_id: str, user_profile_data: dict) -> Optional[UserProfile]:
    """
    Update an existing user profile.
    """
    user_profile = await get_user_profile(session, user_id)
    if user_profile:
        for key, value in user_profile_data.items():
            if hasattr(user_profile, key):
                setattr(user_profile, key, value)
        user_profile.updated_at = datetime.utcnow()
        await session.commit()
        await session.refresh(user_profile)
    return user_profile


async def delete_user_profile(session: AsyncSession, user_id: str) -> bool:
    """
    Delete a user profile by user ID.
    """
    user_profile = await get_user_profile(session, user_id)
    if user_profile:
        await session.delete(user_profile)
        await session.commit()
        return True
    return False


async def get_user_profiles(session: AsyncSession, skip: int = 0, limit: int = 100) -> List[UserProfile]:
    """
    Retrieve multiple user profiles with pagination.
    """
    statement = select(UserProfile).offset(skip).limit(limit)
    result = await session.exec(statement)
    return result.all()

