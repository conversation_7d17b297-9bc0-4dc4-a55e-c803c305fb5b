/**
 * Enhanced Authentication Hook
 * Provides comprehensive authentication state management with error handling and retry mechanisms
 * 
 * Navigation Blocking Fixes:
 * - Added cleanup functions to prevent state updates on unmounted components
 * - Implemented proper mounted checks using mountedRef to avoid memory leaks
 * - Fixed auto-refresh effect to prevent infinite loops and concurrent operations
 * - Added non-blocking navigation with setTimeout for redirects
 * - Enhanced error handling to avoid blocking the main thread
 * - Added proper interval cleanup and operation cancellation
 */

"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { useRouter } from "next/navigation"
import { 
  signIn, 
  signUp, 
  signOut, 
  getAuthState, 
  isSessionValid, 
  onAuthStateChange,
  type AuthState,
  type LoginResponse
} from "@/lib/auth"
import { 
  parseAuthError, 
  parseAuthResponse,
  withRetry, 
  createOperationId, 
  globalRetryManager,
  type AuthError,
  type AuthExpectedState
} from "@/lib/authErrors"
import { signInWithOAuth } from "@/lib/supabaseClient"
import { useAuthToast } from "@/app/components/ui/toast-provider"

export interface AuthStatus {
  isLoading: boolean
  isAuthenticated: boolean
  user: AuthState['user']
  session: AuthState['session']
  authMethod: AuthState['authMethod']
  error: AuthError | null
  retryCount: number
  lastSignupResult?: any
}

export interface AuthOperations {
  // Email/password authentication
  login: (email: string, password: string) => Promise<boolean>
  signup: (email: string, password: string, fullName: string) => Promise<boolean>
  logout: () => Promise<void>
  
  // OAuth authentication
  loginWithOAuth: (provider: 'google' | 'github') => Promise<boolean>
  
  // Session management
  refreshSession: () => Promise<boolean>
  checkSession: () => Promise<boolean>
  
  // Error handling
  clearError: () => void
  retryLastOperation: () => Promise<boolean>
  
  // Utility functions
  canRetry: () => boolean
  getRemainingRetries: () => number
}

export interface UseAuthOptions {
  useSupabase?: boolean
  autoRefresh?: boolean
  checkInterval?: number
  redirectOnError?: boolean
  showToasts?: boolean
}

export function useAuth(options: UseAuthOptions = {}): AuthStatus & AuthOperations {
  const {
    useSupabase = true,
    autoRefresh = true,
    checkInterval = 30000, // 30 seconds
    redirectOnError = false,
    showToasts = true
  } = options

  const router = useRouter()
  const toast = useAuthToast()
  
  // State management
  const [authStatus, setAuthStatus] = useState<AuthStatus>({
    isLoading: true,
    isAuthenticated: false,
    user: null,
    session: null,
    authMethod: null,
    error: null,
    retryCount: 0
  })

  // Refs for tracking operations and cleanup
  const lastOperationRef = useRef<{
    type: string
    operation: () => Promise<any>
    operationId: string
  } | null>(null)
  
  // Ref to track if component is mounted
  const mountedRef = useRef(true)

  // Initialize auth state
  useEffect(() => {
    let mounted = true
    let initializationPromise: Promise<void> | null = null
    
    const initializeAuth = async () => {
      try {
        const authState = getAuthState()
        
        if (mounted) {
          setAuthStatus(prev => ({
            ...prev,
            isLoading: false,
            isAuthenticated: authState.isAuthenticated,
            user: authState.user,
            session: authState.session,
            authMethod: authState.authMethod
          }))
        }
      } catch (error) {
        const authError = parseAuthError(error, 'auth_initialization')
        
        if (mounted) {
          setAuthStatus(prev => ({
            ...prev,
            isLoading: false,
            error: authError
          }))
          
          if (showToasts) {
            toast.showError(authError)
          }
        }
      }
    }

    // Use Promise to avoid race conditions and ensure initialization completes
    initializationPromise = initializeAuth()

    // Set up auth state change listener
    const unsubscribe = onAuthStateChange((authState) => {
      if (mounted) {
        setAuthStatus(prev => ({
          ...prev,
          isAuthenticated: authState.isAuthenticated,
          user: authState.user,
          session: authState.session,
          authMethod: authState.authMethod
        }))
      }
    })

    return () => {
      mounted = false
      unsubscribe()
      // Cancel any pending initialization
      if (initializationPromise) {
        initializationPromise.catch(() => {
          // Ignore errors on cleanup
        })
      }
    }
  }, []) // Remove showToasts and toast dependencies to prevent loops

  // Cleanup effect to prevent memory leaks and state updates on unmounted components
  useEffect(() => {
    return () => {
      mountedRef.current = false
      // Clear any pending operations
      lastOperationRef.current = null
    }
  }, [])

  // Helper function to handle auth operations with retry logic
  const handleAuthOperation = useCallback(async (
    operationType: string,
    operation: () => Promise<any>,
    params: string[] = []
  ): Promise<boolean> => {
    // Early return if component is unmounted
    if (!mountedRef.current) {
      return false
    }

    const operationId = createOperationId(operationType, ...params)
    
    // Store operation for retry
    lastOperationRef.current = {
      type: operationType,
      operation,
      operationId
    }

    // Only update state if component is still mounted
    if (mountedRef.current) {
      setAuthStatus(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }))
    }

    try {
      const result = await withRetry(operationId, operation, operationType)
      
      // Check if component is still mounted before updating state
      if (mountedRef.current) {
        setAuthStatus(prev => ({
          ...prev,
          isLoading: false,
          error: null,
          retryCount: 0
        }))

        // Show success toast for certain operations (avoid in operation handler to prevent loops)
        // Toast will be handled by the calling component
      }

      return true
    } catch (error) {
      const authError = error as AuthError
      const retryCount = globalRetryManager.getRemainingAttempts(operationId)

      // Check if component is still mounted before updating state
      if (mountedRef.current) {
        setAuthStatus(prev => ({
          ...prev,
          isLoading: false,
          error: authError,
          retryCount
        }))

        // Error will be available in auth.error for component to handle
        // Avoid showing toast here to prevent loops

        // Redirect on certain errors if configured
        if (redirectOnError && ['SESSION_EXPIRED', 'INVALID_TOKEN'].includes(authError.code)) {
          // Use setTimeout to avoid blocking the main thread
          setTimeout(() => {
            if (mountedRef.current) {
              router.push('/login')
            }
          }, 0)
        }
      }

      return false
    }
  }, [showToasts, toast, redirectOnError, router])

  // Authentication operations
  const login = useCallback(async (email: string, password: string): Promise<boolean> => {
    return handleAuthOperation('login', async () => {
      return await signIn(email, password, useSupabase)
    }, [email])
  }, [handleAuthOperation, useSupabase])

  const signup = useCallback(async (email: string, password: string, fullName: string): Promise<boolean> => {
    return handleAuthOperation('signup', async () => {
      console.log('useAuth signup - using Supabase:', useSupabase)
      const result = await signUp(email, password, fullName, useSupabase)
      console.log('useAuth signup result:', result)

      const { error, expectedState, isError } = parseAuthResponse(result, 'signup')

      if (isError) {
        // If there's an error, throw it so it can be handled by the catch block
        throw error
      }

      if (expectedState && mountedRef.current) {
        // Handle expected state for email confirmation sent or pending
        toast.showExpectedState(expectedState)
      }
      
      // Store the signup result for the component to access
      if (mountedRef.current) {
        setAuthStatus(prev => ({
          ...prev,
          lastSignupResult: result
        }))
      }
      
      // Return true for successful signup (either immediate login or email confirmation required)
      return result !== null && result !== undefined
    }, [email])
  }, [handleAuthOperation, useSupabase, toast])

  const logout = useCallback(async (): Promise<void> => {
    await handleAuthOperation('logout', async () => {
      await signOut()
    })
  }, [handleAuthOperation])

  const loginWithOAuth = useCallback(async (provider: 'google' | 'github'): Promise<boolean> => {
    return handleAuthOperation('oauth', async () => {
      const result = await signInWithOAuth(provider)
      if (result.error) {
        throw result.error
      }
      return result.data
    }, [provider])
  }, [handleAuthOperation])

  const refreshSession = useCallback(async (): Promise<boolean> => {
    return handleAuthOperation('refresh', async () => {
      const { refreshSession } = await import('@/lib/auth')
      const newToken = await refreshSession()
      if (!newToken) {
        throw new Error('Failed to refresh session')
      }
      return newToken
    })
  }, [handleAuthOperation])

  // Auto-refresh session with proper cleanup and non-blocking operations
  useEffect(() => {
    if (!autoRefresh || !authStatus.isAuthenticated) return

    let intervalId: NodeJS.Timeout | null = null
    let isSessionCheckActive = false

    const sessionCheckOperation = async () => {
      // Prevent concurrent session checks
      if (isSessionCheckActive) return
      
      isSessionCheckActive = true
      try {
        const isValid = await isSessionValid()
        if (!isValid) {
          // Use async refresh without blocking
          refreshSession().catch((error) => {
            console.error('Session refresh failed:', error)
          })
        }
      } catch (error) {
        console.error('Session check failed:', error)
      } finally {
        isSessionCheckActive = false
      }
    }

    intervalId = setInterval(sessionCheckOperation, checkInterval)

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
        intervalId = null
      }
      isSessionCheckActive = false
    }
  }, [autoRefresh, authStatus.isAuthenticated, checkInterval, refreshSession])

  const checkSession = useCallback(async (): Promise<boolean> => {
    try {
      const isValid = await isSessionValid()
      
      // Only update state if component is still mounted
      if (mountedRef.current) {
        setAuthStatus(prev => ({
          ...prev,
          isAuthenticated: isValid
        }))
      }

      return isValid
    } catch (error) {
      const authError = parseAuthError(error, 'session_check')
      
      // Only update state if component is still mounted
      if (mountedRef.current) {
        setAuthStatus(prev => ({
          ...prev,
          error: authError
        }))
      }

      return false
    }
  }, [])

  const clearError = useCallback(() => {
    // Only update state if component is still mounted
    if (mountedRef.current) {
      setAuthStatus(prev => ({
        ...prev,
        error: null,
        retryCount: 0
      }))
    }
  }, [])

  const retryLastOperation = useCallback(async (): Promise<boolean> => {
    if (!lastOperationRef.current || !mountedRef.current) {
      return false
    }

    const { operation, operationId } = lastOperationRef.current
    
    // Check if retry is allowed
    if (!authStatus.error?.retryable || !globalRetryManager.shouldRetry(operationId, authStatus.error)) {
      return false
    }

    // Only update state if component is still mounted
    if (mountedRef.current) {
      setAuthStatus(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }))
    }

    try {
      const result = await operation()
      
      // Only update state if component is still mounted
      if (mountedRef.current) {
        setAuthStatus(prev => ({
          ...prev,
          isLoading: false,
          error: null,
          retryCount: 0
        }))

        if (showToasts) {
          toast.showSuccess('Operation succeeded!')
        }
      }

      return true
    } catch (error) {
      const authError = parseAuthError(error, 'retry_operation')
      
      // Only update state if component is still mounted
      if (mountedRef.current) {
        setAuthStatus(prev => ({
          ...prev,
          isLoading: false,
          error: authError
        }))

        if (showToasts) {
          toast.showError(authError)
        }
      }

      return false
    }
  }, [authStatus.error, showToasts, toast])

  const canRetry = useCallback(() => {
    return !!(authStatus.error?.retryable && lastOperationRef.current)
  }, [authStatus.error])

  const getRemainingRetries = useCallback(() => {
    if (!lastOperationRef.current) return 0
    return globalRetryManager.getRemainingAttempts(lastOperationRef.current.operationId)
  }, [])

  return {
    // Status
    ...authStatus,
    
    // Operations
    login,
    signup,
    logout,
    loginWithOAuth,
    refreshSession,
    checkSession,
    clearError,
    retryLastOperation,
    canRetry,
    getRemainingRetries
  }
}
