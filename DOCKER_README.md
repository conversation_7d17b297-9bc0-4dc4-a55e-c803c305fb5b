# EVOLVE Docker Development Environment

This document provides instructions for setting up and using the EVOLVE development environment with Docker.

## Prerequisites

- Docker Desktop installed and running
- Docker Compose v2.0 or higher
- At least 4GB RAM available for Docker
- Ports 80, 443, 5432, 6379, 8080, 8081, 8082, 8108, 9000, 9001 available

## Quick Start

1. **Clone the repository and navigate to the project root:**
   ```bash
   cd /path/to/evolve
   ```

2. **Copy the environment file:**
   ```bash
   cp .env.example .env
   ```

3. **Update the `.env` file with your configuration:**
   - Replace placeholder values with actual secrets
   - Set your Supabase URL and keys
   - Configure any external service API keys

4. **Start all services:**
   ```bash
   docker-compose up -d
   ```

5. **Verify services are running:**
   ```bash
   docker-compose ps
   ```

## Services Overview

### Core Services

| Service | Port | Description | Admin UI |
|---------|------|-------------|----------|
| PostgreSQL | 5432 | Primary database | [Adminer](http://localhost:8081) |
| Redis | 6379 | Cache & sessions | [Redis Commander](http://localhost:8082) |
| MinIO | 9000 | S3-compatible storage | [MinIO Console](http://localhost:9001) |
| Typesense | 8108 | Search engine | - |
| LocalAI | 8080 | Local LLM inference | - |
| Nginx | 80/443 | Reverse proxy | - |

### Management UIs

- **Adminer**: Database management at http://localhost:8081
- **Redis Commander**: Redis management at http://localhost:8082
- **MinIO Console**: Storage management at http://localhost:9001

## Common Commands

### Start Services
```bash
# Start all services
docker-compose up -d

# Start specific service
docker-compose up -d postgres

# Start with logs
docker-compose up
```

### Stop Services
```bash
# Stop all services
docker-compose down

# Stop and remove volumes (⚠️ destroys data)
docker-compose down -v
```

### View Logs
```bash
# View all logs
docker-compose logs

# View logs for specific service
docker-compose logs postgres

# Follow logs in real-time
docker-compose logs -f
```

### Service Management
```bash
# Restart a service
docker-compose restart postgres

# Rebuild a service
docker-compose build postgres

# Scale a service
docker-compose up -d --scale localai=2
```

## Configuration

### Environment Variables

Key environment variables you should customize:

```env
# Database
POSTGRES_PASSWORD=your-secure-password
DATABASE_URL=postgresql://postgres:your-secure-password@localhost:5432/evolve

# Redis
REDIS_PASSWORD=your-redis-password

# MinIO
MINIO_ROOT_USER=your-minio-user
MINIO_ROOT_PASSWORD=your-minio-password

# Security
JWT_SECRET=your-jwt-secret-key
SESSION_SECRET=your-session-secret-key

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
```

### Port Customization

You can customize ports by setting environment variables:

```env
POSTGRES_PORT=5433
REDIS_PORT=6380
MINIO_PORT=9001
MINIO_CONSOLE_PORT=9002
```

## Database Setup

### Initial Setup

The PostgreSQL service automatically runs the initialization script at `scripts/init-db.sql` on first startup.

### Connecting to Database

```bash
# Using Docker
docker-compose exec postgres psql -U postgres -d evolve

# Using external tool
psql -h localhost -p 5432 -U postgres -d evolve
```

### Database Migrations

For application migrations, use your preferred migration tool:

```bash
# Example with Prisma
npx prisma migrate dev

# Example with TypeORM
npm run migration:run
```

## Storage Setup

### MinIO Configuration

1. Access MinIO Console at http://localhost:9001
2. Login with credentials from your `.env` file
3. Create required buckets:
   - `evolve-storage`
   - `evolve-avatars`
   - `evolve-documents`

### S3 Client Configuration

Use these settings in your application:

```javascript
const s3Config = {
  endpoint: 'http://localhost:9000',
  accessKeyId: process.env.MINIO_ACCESS_KEY,
  secretAccessKey: process.env.MINIO_SECRET_KEY,
  s3ForcePathStyle: true,
  signatureVersion: 'v4'
};
```

## Search Setup

### Typesense Configuration

Typesense is available at `http://localhost:8108` with API key from your `.env` file.

Example client configuration:

```javascript
const typesenseConfig = {
  nodes: [{
    host: 'localhost',
    port: '8108',
    protocol: 'http'
  }],
  apiKey: process.env.TYPESENSE_API_KEY,
  connectionTimeoutSeconds: 10
};
```

## AI Setup

### LocalAI Configuration

LocalAI provides OpenAI-compatible APIs at `http://localhost:8080`.

Example usage:

```javascript
const response = await fetch('http://localhost:8080/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model: 'gpt-3.5-turbo',
    messages: [{ role: 'user', content: 'Hello!' }]
  })
});
```

## Health Checks

All services include health checks. Check service status:

```bash
# View health status
docker-compose ps

# Check specific service health
docker-compose exec postgres pg_isready -U postgres
docker-compose exec redis redis-cli ping
```

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Find process using port
   lsof -i :5432
   
   # Kill process
   kill -9 <PID>
   ```

2. **Permission issues:**
   ```bash
   # Fix Docker permissions (Linux/Mac)
   sudo chown -R $USER:$USER ./data/
   ```

3. **Memory issues:**
   ```bash
   # Increase Docker memory limit
   # Docker Desktop > Settings > Resources > Memory
   ```

4. **Service won't start:**
   ```bash
   # Check logs
   docker-compose logs <service-name>
   
   # Restart service
   docker-compose restart <service-name>
   ```

### Reset Everything

⚠️ **Warning**: This will delete all data!

```bash
# Stop all services and remove volumes
docker-compose down -v

# Remove all containers, images, and volumes
docker system prune -a --volumes

# Restart
docker-compose up -d
```

## Development Workflow

### Recommended Development Process

1. Start Docker services:
   ```bash
   docker-compose up -d
   ```

2. Run your application locally:
   ```bash
   # Frontend
   cd apps/frontend
   npm run dev
   
   # Backend
   cd apps/api
   npm run dev
   ```

3. Access services:
   - Frontend: http://localhost:3000
   - API: http://localhost:3001
   - Database: localhost:5432
   - Redis: localhost:6379

### Production Considerations

For production deployment:

1. Use strong passwords and secrets
2. Enable SSL/TLS
3. Configure firewall rules
4. Set up monitoring and logging
5. Regular backups
6. Update Docker images regularly

## Support

For issues with the Docker setup:

1. Check the logs: `docker-compose logs`
2. Verify your `.env` configuration
3. Ensure all required ports are available
4. Check Docker Desktop status
5. Consult the project documentation

## Additional Resources

- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [PostgreSQL Docker Image](https://hub.docker.com/_/postgres)
- [Redis Docker Image](https://hub.docker.com/_/redis)
- [MinIO Docker Image](https://hub.docker.com/r/minio/minio)
- [Typesense Docker Image](https://hub.docker.com/r/typesense/typesense)
- [LocalAI Documentation](https://localai.io/)
