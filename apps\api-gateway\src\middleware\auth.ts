import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

// Extend Express Request interface to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        sub: string;
        user_id: string;
        email?: string;
        exp?: number;
        iat?: number;
        type?: string;
      };
    }
  }
}

export interface JWTPayload {
  sub: string;
  exp: number;
  iat: number;
  type?: string;
  email?: string;
}

/**
 * JWT Authentication Middleware
 * Validates JWT tokens and attaches user information to request object
 */
export const authenticateToken = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Extract Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      res.status(401).json({
        error: 'access_denied',
        error_description: 'Authorization header is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Check if the header follows the Bearer token format
    const tokenMatch = authHeader.match(/^Bearer\s+(.+)$/);
    if (!tokenMatch) {
      res.status(401).json({
        error: 'invalid_token_format',
        error_description: 'Authorization header must be in format: Bearer <token>',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const token = tokenMatch[1];

    // Get JWT secret from environment variables
    const jwtSecret = process.env.JWT_SECRET_KEY;
    if (!jwtSecret) {
      console.error('JWT_SECRET_KEY environment variable is not configured');
      res.status(500).json({
        error: 'server_configuration_error',
        error_description: 'Authentication service is not properly configured',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Verify the JWT token
    const decoded = jwt.verify(token, jwtSecret) as JWTPayload;

    // Check if token type is access token (if type field exists)
    if (decoded.type && decoded.type !== 'access') {
      res.status(401).json({
        error: 'invalid_token_type',
        error_description: 'Only access tokens are allowed for API requests',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Check token expiration (additional safety check)
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp < currentTime) {
      res.status(401).json({
        error: 'token_expired',
        error_description: 'Access token has expired',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Attach user information to request object
    req.user = {
      sub: decoded.sub,
      user_id: decoded.sub, // Alias for convenience
      email: decoded.email,
      exp: decoded.exp,
      iat: decoded.iat,
      type: decoded.type
    };

    // Log successful authentication (for debugging)
    console.log(`✅ Authenticated user: ${decoded.sub} for ${req.method} ${req.originalUrl}`);

    next();
  } catch (error) {
    // Handle different types of JWT errors
    if (error instanceof jwt.TokenExpiredError) {
      res.status(401).json({
        error: 'token_expired',
        error_description: 'Access token has expired',
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({
        error: 'invalid_token',
        error_description: 'Invalid access token signature or format',
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (error instanceof jwt.NotBeforeError) {
      res.status(401).json({
        error: 'token_not_active',
        error_description: 'Access token is not yet valid',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Log unexpected errors
    console.error('Unexpected authentication error:', error);
    res.status(500).json({
      error: 'authentication_error',
      error_description: 'An unexpected error occurred during authentication',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Optional authentication middleware
 * Attaches user information if valid token is provided, but doesn't reject requests without tokens
 */
export const optionalAuthentication = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    // No token provided, continue without authentication
    next();
    return;
  }

  // Use the same logic as authenticateToken, but don't reject on missing token
  authenticateToken(req, res, next);
};

/**
 * Middleware to check if user is authenticated
 * Utility function to check if req.user exists
 */
export const requireAuth = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      error: 'authentication_required',
      error_description: 'This endpoint requires authentication',
      timestamp: new Date().toISOString()
    });
    return;
  }
  next();
};

/**
 * Admin role checking middleware (for future use)
 * Can be extended to check specific roles or permissions
 */
export const requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      error: 'authentication_required',
      error_description: 'This endpoint requires authentication',
      timestamp: new Date().toISOString()
    });
    return;
  }

  // For now, this is a placeholder. In the future, you could check user roles
  // from the JWT payload or make a call to the user service
  console.log(`Admin access attempted by user: ${req.user.sub}`);
  next();
};
