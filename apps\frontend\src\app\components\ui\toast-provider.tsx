/**
 * Toast Provider and Context
 * Manages toast notifications globally across the application
 */

"use client"

import * as React from "react"
import { createPortal } from "react-dom"
import { Toast, type ToastProps } from "./toast"
import { AuthError, AuthExpectedState } from "@/lib/authErrors"

export interface ToastData extends Omit<ToastProps, 'onClose'> {
  id: string
  timestamp: number
}

interface ToastContextValue {
  toasts: ToastData[]
  addToast: (toast: Omit<ToastData, 'id' | 'timestamp'>) => string
  removeToast: (id: string) => void
  showAuthError: (error: AuthError, onRetry?: () => void) => string
  showAuthSuccess: (message: string, title?: string) => string
  showAuthInfo: (message: string, title?: string) => string
  showExpectedState: (state: AuthExpectedState, onAction?: () => void) => string
  clearAllToasts: () => void
}

const ToastContext = React.createContext<ToastContextValue | undefined>(undefined)

export function useToast(): ToastContextValue {
  const context = React.useContext(ToastContext)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

interface ToastProviderProps {
  children: React.ReactNode
  maxToasts?: number
}

export function ToastProvider({ children, maxToasts = 5 }: ToastProviderProps) {
  const [toasts, setToasts] = React.useState<ToastData[]>([])
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  const addToast = React.useCallback((toast: Omit<ToastData, 'id' | 'timestamp'>) => {
    const id = Math.random().toString(36).substring(2, 9)
    const timestamp = Date.now()
    
    setToasts(currentToasts => {
      const newToasts = [...currentToasts, { ...toast, id, timestamp }]
      // Keep only the most recent toasts
      return newToasts.slice(-maxToasts)
    })
    
    return id
  }, [maxToasts])

  const removeToast = React.useCallback((id: string) => {
    setToasts(currentToasts => currentToasts.filter(toast => toast.id !== id))
  }, [])

  const showAuthError = React.useCallback((error: AuthError, onRetry?: () => void) => {
    return addToast({
      variant: "destructive",
      title: "Authentication Error",
      description: error.userMessage,
      duration: error.retryable ? 8000 : 6000,
      retryable: error.retryable,
      onRetry: onRetry,
      retryText: "Try Again"
    })
  }, [addToast])

  const showAuthSuccess = React.useCallback((message: string, title: string = "Success") => {
    return addToast({
      variant: "success",
      title,
      description: message,
      duration: 4000
    })
  }, [addToast])

  const showAuthInfo = React.useCallback((message: string, title: string = "Information") => {
    return addToast({
      variant: "info",
      title,
      description: message,
      duration: 5000
    })
  }, [addToast])

  const showExpectedState = React.useCallback((state: AuthExpectedState, onAction?: () => void) => {
    return addToast({
      variant: state.isSuccess ? "success" : "info",
      title: state.isSuccess ? "Success" : "Action Required",
      description: state.userMessage,
      duration: state.requiresAction ? 8000 : 5000,
      retryable: state.requiresAction,
      onRetry: onAction,
      retryText: state.actionText || "Continue"
    })
  }, [addToast])

  const clearAllToasts = React.useCallback(() => {
    setToasts([])
  }, [])

  const contextValue: ToastContextValue = React.useMemo(() => ({
    toasts,
    addToast,
    removeToast,
    showAuthError,
    showAuthSuccess,
    showAuthInfo,
    showExpectedState,
    clearAllToasts
  }), [toasts, addToast, removeToast, showAuthError, showAuthSuccess, showAuthInfo, showExpectedState, clearAllToasts])

  // Toast container component
  const ToastContainer = React.useMemo(() => {
    if (!mounted) return null

    return createPortal(
      <div 
        className="fixed top-4 right-4 z-50 flex flex-col space-y-2 max-w-sm w-full pointer-events-none"
        aria-live="polite"
        aria-label="Notifications"
      >
        {toasts.map(toast => (
          <div key={toast.id} className="pointer-events-auto">
            <Toast
              {...toast}
              onClose={() => removeToast(toast.id)}
            />
          </div>
        ))}
      </div>,
      document.body
    )
  }, [mounted, toasts, removeToast])

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      {ToastContainer}
    </ToastContext.Provider>
  )
}

// Convenience hook for authentication-specific toasts
export function useAuthToast() {
  const { showAuthError, showAuthSuccess, showAuthInfo, showExpectedState } = useToast()
  
  return {
    showError: showAuthError,
    showSuccess: showAuthSuccess,
    showInfo: showAuthInfo,
    showExpectedState: showExpectedState
  }
}
