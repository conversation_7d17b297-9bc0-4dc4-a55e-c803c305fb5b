import * as React from "react"
import { cn } from "@/app/lib/utils"
import { Alert, AlertDescription } from "@/app/components/ui/alert"
import { SketchButton } from "@/app/components/common/SketchButton"
import { CheckCircle, XCircle, AlertCircle, Info } from "lucide-react"

interface StatusMessageProps {
  message?: string
  type?: "error" | "success" | "warning" | "info"
  className?: string
  onAction?: () => void
  actionLabel?: string
  showAction?: boolean
  icon?: React.ReactNode
  title?: string
}

const StatusMessage: React.FC<StatusMessageProps> = ({
  message,
  type = "error",
  className,
  onAction,
  actionLabel,
  showAction = false,
  icon,
  title
}) => {
  if (!message) return null

  // Determine variant based on type
  const getVariant = () => {
    switch (type) {
      case "success":
        return "success"
      case "error":
        return "destructive"
      case "warning":
        return "default"
      case "info":
        return "default"
      default:
        return "destructive"
    }
  }

  // Get default icon based on type
  const getDefaultIcon = () => {
    switch (type) {
      case "success":
        return <CheckCircle className="h-4 w-4" />
      case "error":
        return <XCircle className="h-4 w-4" />
      case "warning":
        return <AlertCircle className="h-4 w-4" />
      case "info":
        return <Info className="h-4 w-4" />
      default:
        return <XCircle className="h-4 w-4" />
    }
  }

  // Get styling based on type
  const getTypeStyles = () => {
    switch (type) {
      case "success":
        return {
          container: "border-green-400 bg-green-50",
          icon: "text-green-600",
          text: "text-green-800",
          title: "text-green-900",
          separator: "border-green-200",
          button: "bg-green-100 hover:bg-green-200 text-green-700 border-green-300"
        }
      case "error":
        return {
          container: "border-red-400 bg-red-50",
          icon: "text-red-600",
          text: "text-red-800",
          title: "text-red-900",
          separator: "border-red-200",
          button: "bg-red-100 hover:bg-red-200 text-red-700 border-red-300"
        }
      case "warning":
        return {
          container: "border-yellow-400 bg-yellow-50",
          icon: "text-yellow-600",
          text: "text-yellow-800",
          title: "text-yellow-900",
          separator: "border-yellow-200",
          button: "bg-yellow-100 hover:bg-yellow-200 text-yellow-700 border-yellow-300"
        }
      case "info":
        return {
          container: "border-blue-400 bg-blue-50",
          icon: "text-blue-600",
          text: "text-blue-800",
          title: "text-blue-900",
          separator: "border-blue-200",
          button: "bg-blue-100 hover:bg-blue-200 text-blue-700 border-blue-300"
        }
      default:
        return {
          container: "border-red-400 bg-red-50",
          icon: "text-red-600",
          text: "text-red-800",
          title: "text-red-900",
          separator: "border-red-200",
          button: "bg-red-100 hover:bg-red-200 text-red-700 border-red-300"
        }
    }
  }

  const styles = getTypeStyles()
  const displayIcon = icon || getDefaultIcon()

  return (
    <Alert 
      variant={getVariant()} 
      className={cn(
        "animate-in fade-in-50 duration-300",
        styles.container,
        className
      )}
    >
      <div className={cn("flex items-start gap-3", styles.icon)}>
        {displayIcon}
        <div className="flex-1 space-y-2">
          <AlertDescription>
            <div className="space-y-2">
              {title && (
                <h4 className={cn("font-medium font-heading", styles.title)}>
                  {title}
                </h4>
              )}
              <p className={cn("text-sm leading-relaxed", styles.text)}>
                {message}
              </p>
              
              {showAction && onAction && actionLabel && (
                <div className={cn("pt-3 border-t", styles.separator)}>
                  <SketchButton
                    onClick={onAction}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "text-sm transition-all duration-200",
                      styles.button
                    )}
                  >
                    {actionLabel}
                  </SketchButton>
                </div>
              )}
            </div>
          </AlertDescription>
        </div>
      </div>
    </Alert>
  )
}

export { StatusMessage }
