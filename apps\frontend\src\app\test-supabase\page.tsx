"use client"

import { useState } from 'react'
import { supabase } from '@/app/lib/supabaseClient'

export default function TestSupabasePage() {
  const [result, setResult] = useState<string>('')
  
  const testConnection = async () => {
    try {
      setResult('Testing connection...')
      
      // Test basic connection
      const { error } = await supabase.from('_realtime').select('*').limit(1)
      
      if (error) {
        setResult(`Connection test failed: ${error.message}`)
      } else {
        setResult('✅ Supabase connection successful!')
      }
    } catch (err) {
      setResult(`❌ Connection failed: ${err}`)
    }
  }
  
  const testSignup = async () => {
    try {
      setResult('Testing signup...')
      
      const testEmail = `test-${Date.now()}@example.com`
      const { data, error } = await supabase.auth.signUp({
        email: testEmail,
        password: 'testpass123'
      })
      
      if (error) {
        setResult(`❌ Signup test failed: ${error.message}`)
      } else if (data.user && !data.session) {
        setResult(`✅ Signup works! Email confirmation required for ${testEmail}`)
      } else if (data.user && data.session) {
        setResult(`✅ Signup works! Auto-login enabled for ${testEmail}`)
      }
    } catch (err) {
      setResult(`❌ Signup test failed: ${err}`)
    }
  }
  
  return (
    <div className="min-h-screen p-8">
      <h1 className="text-2xl font-bold mb-6">Supabase Test Page</h1>
      
      <div className="space-y-4">
        <button 
          onClick={testConnection}
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          Test Connection
        </button>
        
        <button 
          onClick={testSignup}
          className="bg-green-500 text-white px-4 py-2 rounded ml-4"
        >
          Test Signup
        </button>
      </div>
      
      <div className="mt-6 p-4 border rounded">
        <h2 className="font-bold">Test Result:</h2>
        <pre>{result}</pre>
      </div>
      
      <div className="mt-6 p-4 bg-gray-100 rounded">
        <h2 className="font-bold">Current Config:</h2>
        <p>Supabase URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}</p>
        <p>Anon Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20)}...</p>
      </div>
    </div>
  )
}
