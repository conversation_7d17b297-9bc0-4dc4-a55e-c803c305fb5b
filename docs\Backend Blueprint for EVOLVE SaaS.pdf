# Backend Blueprint for EVOLVE SaaS

## Authentication Service

EVOLVE uses a secure authentication system with the following components:

### Environment Variables

- **SUPABASE_URL**: Base URL of your Supabase instance.
- **SUPABASE_ANON_KEY**: Anonymous key for public requests.
- **SUPABASE_SERVICE_ROLE_KEY**: Service role key for elevated permissions.
- **JWT_SECRET_KEY**: Secret key for signing JWT tokens.
- **JWT_ALGORITHM**: Algorithm used for JWT tokens (e.g., HS256).
- **ACCESS_TOKEN_EXPIRE_MINUTES**: Expiration time for access tokens in minutes.
- **REFRESH_TOKEN_EXPIRE_DAYS**: Expiration time for refresh tokens in days.

Ensure these variables are set in your `auth-service/.env` file.

### Docker Integration

- Ensure the `auth-service` is included in your `docker-compose.yml` and linked with all necessary services (e.g., database, cache).
- Use Docker Compose for a seamless setup and deployment.

### Security Practices

- Use HTTPS in production environments to protect token integrity.
- Manage secrets through environment variables or a secret management service.
- Regularly rotate keys and monitor access logs.

## API Gateway

Configure the API Gateway with the following:

### Environment Variables

- **SUPABASE_URL**: Base URL of your Supabase instance.
- **SUPABASE_ANON_KEY**: Anonymous key for client requests.
- **SUPABASE_SERVICE_ROLE_KEY**: Role key for internal service interactions.

These should be configured in `api-gateway/.env`.

### Security Practices

- Authenticate requests using JWT tokens issued by the `auth-service`.
- Implement rate limiting and logging for monitoring.

Ensure these configurations align with your deployment and security standards.
