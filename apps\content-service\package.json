{"name": "@evolve/content-service", "version": "1.0.0", "description": "EVOLVE Content Service - FastAPI content management and delivery service", "private": true, "scripts": {"dev": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8002 --reload", "start": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8002", "content:dev": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8002 --reload", "content:start": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8002", "build": "echo 'Content Service build complete'", "content:build": "echo 'Content Service build complete'", "test": "python -m pytest", "content:test": "python -m pytest", "lint": "python -m flake8 app/", "content:lint": "python -m flake8 app/", "format": "python -m black app/", "format:check": "python -m black --check app/", "type-check": "python -m mypy app/", "install": "pip install -r requirements.txt"}, "keywords": ["<PERSON><PERSON><PERSON>", "content-management", "cms", "backend", "python"], "author": "", "license": "ISC"}