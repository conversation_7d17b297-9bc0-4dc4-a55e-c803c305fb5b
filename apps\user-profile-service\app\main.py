"""
EVOLVE User Profile Service - Main FastAPI application
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager
from typing import Optional
import os

from app.core.config import settings
from app.core.database import init_db, close_db
from app.core.logging import setup_logging
from app.api.v1.endpoints import profiles

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan context manager for FastAPI application"""
    # Startup
    print("🚀 EVOLVE User Profile Service starting up...")
    
    # Initialize logging
    setup_logging()
    
    try:
        await init_db()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        raise
    
    yield
    
    # Shutdown
    print("🛑 EVOLVE User Profile Service shutting down...")
    await close_db()

# Initialize FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    description="User profile and preferences management service for EVOLVE platform",
    version=settings.APP_VERSION,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(profiles.router, prefix="/api/v1/profiles", tags=["profiles"])

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "user-profile-service"}

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "health": "/health"
    }

# Simple /me endpoint at root level for easy access
@app.get("/me")
async def get_me_simple(
    x_user_id: Optional[str] = Header(None),
    x_user_email: Optional[str] = Header(None),
    x_authenticated: Optional[str] = Header(None)
):
    """
    Simple /me endpoint that expects user information from gateway headers
    """
    if not x_user_id or x_authenticated != "true":
        raise HTTPException(
            status_code=401,
            detail={
                "error": "authentication_required",
                "error_description": "This endpoint requires authentication"
            }
        )
    
    return {
        "message": "Successfully accessed protected user profile route",
        "userId": x_user_id or "from_gateway_placeholder",
        "email": x_user_email or "",
        "timestamp": "2025-01-11T16:25:00Z"
    }

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "internal_server_error",
            "error_description": "An unexpected error occurred",
            "timestamp": "2025-01-11T16:25:00Z"
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
