import * as React from "react";
import { cn } from "@/app/lib/utils";

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

const getPasswordStrength = (password: string) => {
  let score = 0;
  if (password.length >= 8) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/[0-9]/.test(password)) score++;
  if (/[@$!%*?&#]/.test(password)) score++;
  return score;
};

const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  className
}) => {
  const score = getPasswordStrength(password);

  const strength = ["Weak", "Fair", "Good", "Strong"][score] || "Weak";
  const color = ["text-red-500", "text-yellow-500", "text-blue-500", "text-green-500"][score] || "text-red-500";

  return (
    <p className={cn("text-sm mt-1", color, className)}>
      Password Strength: {strength}
    </p>
  );
};

export { PasswordStrengthIndicator };
