{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**", ".next/**", "!.next/cache/**", "__pycache__/**"], "cache": true}, "build:frontend": {"dependsOn": ["@evolve/ui-components#build", "@evolve/shared-types#build", "@evolve/utils#build", "@evolve/hooks#build"], "outputs": [".next/**", "!.next/cache/**", "build/**"], "cache": true}, "build:backend": {"dependsOn": ["ai:build", "auth:build", "content:build", "user-profile:build"], "outputs": ["__pycache__/**", "*.pyc"], "cache": true}, "build:shared": {"dependsOn": [], "outputs": ["dist/**"], "cache": true}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "dev:frontend": {"dependsOn": ["@evolve/ui-components#build", "@evolve/shared-types#build", "@evolve/utils#build"], "cache": false, "persistent": true}, "dev:backend": {"dependsOn": ["ai:dev", "auth:dev", "content:dev", "user-profile:dev"], "cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"], "outputs": [], "cache": true}, "lint:fix": {"outputs": [], "cache": false}, "type-check": {"dependsOn": ["^build"], "outputs": [], "cache": true}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**", ".pytest_cache/**", "test-results/**", "*.xml"], "cache": true}, "test:watch": {"cache": false, "persistent": true}, "test:unit": {"dependsOn": ["^build"], "outputs": ["coverage/**", ".pytest_cache/**"], "cache": true}, "test:integration": {"dependsOn": ["^build"], "outputs": ["coverage/**", ".pytest_cache/**"], "cache": true}, "clean": {"cache": false}, "format": {"outputs": [], "cache": true}, "format:check": {"outputs": [], "cache": true}, "install": {"cache": false}, "start": {"dependsOn": ["build"], "cache": false, "persistent": true}, "docker:build": {"dependsOn": ["build"], "outputs": [], "cache": false}, "ai:start": {"cache": false, "persistent": true}, "ai:dev": {"dependsOn": ["@evolve/shared-types#build"], "cache": false, "persistent": true}, "ai:build": {"outputs": ["__pycache__/**", "*.pyc"], "cache": true}, "ai:test": {"outputs": ["coverage/**", ".pytest_cache/**"], "cache": true}, "ai:lint": {"outputs": [], "cache": true}, "auth:start": {"cache": false, "persistent": true}, "auth:dev": {"dependsOn": ["@evolve/shared-types#build"], "cache": false, "persistent": true}, "auth:build": {"outputs": ["__pycache__/**", "*.pyc"], "cache": true}, "auth:test": {"outputs": ["coverage/**", ".pytest_cache/**"], "cache": true}, "auth:lint": {"outputs": [], "cache": true}, "content:start": {"cache": false, "persistent": true}, "content:dev": {"dependsOn": ["@evolve/shared-types#build"], "cache": false, "persistent": true}, "content:build": {"outputs": ["__pycache__/**", "*.pyc"], "cache": true}, "content:test": {"outputs": ["coverage/**", ".pytest_cache/**"], "cache": true}, "content:lint": {"outputs": [], "cache": true}, "user-profile:start": {"cache": false, "persistent": true}, "user-profile:dev": {"dependsOn": ["@evolve/shared-types#build"], "cache": false, "persistent": true}, "user-profile:build": {"outputs": ["__pycache__/**", "*.pyc"], "cache": true}, "user-profile:test": {"outputs": ["coverage/**", ".pytest_cache/**"], "cache": true}, "user-profile:lint": {"outputs": [], "cache": true}, "api-gateway:start": {"dependsOn": ["ai:start", "auth:start", "content:start", "user-profile:start"], "cache": false, "persistent": true}, "api-gateway:dev": {"dependsOn": ["ai:dev", "auth:dev", "content:dev", "user-profile:dev"], "cache": false, "persistent": true}, "api-gateway:build": {"dependsOn": ["ai:build", "auth:build", "content:build", "user-profile:build"], "outputs": ["__pycache__/**", "*.pyc"], "cache": true}, "api-gateway:test": {"outputs": ["coverage/**", ".pytest_cache/**"], "cache": true}, "api-gateway:lint": {"outputs": [], "cache": true}}, "globalDependencies": ["package.json", "pnpm-lock.yaml", "turbo.json", ".env", ".env.local", ".env.example", "docker-compose.yml", "requirements.txt", "pyproject.toml"], "globalEnv": ["NODE_ENV", "SUPABASE_URL", "SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY", "NEXTAUTH_SECRET", "NEXTAUTH_URL", "OPENAI_API_KEY", "ANTHROPIC_API_KEY", "GEMINI_API_KEY", "REDIS_URL", "NEO4J_URI", "NEO4J_USERNAME", "NEO4J_PASSWORD", "TYPESENSE_API_KEY", "TYPESENSE_HOST", "MINIO_ROOT_USER", "MINIO_ROOT_PASSWORD", "MINIO_ENDPOINT"], "remoteCache": {"enabled": false}}