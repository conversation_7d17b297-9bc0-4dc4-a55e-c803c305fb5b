"""
Profile endpoints for EVOLVE User Profile Service
"""

from fastapi import APIRouter, HTTPException, Header, Depends, status
from typing import Optional
import datetime
import logging

from app.schemas import UserProfileUpdate, UserProfileRead, UserProfileCreate, UserProfilePublic
from app.services.user_profile_service import UserProfileService, create_user_profile_service
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

# JWT middleware dependency function
def get_current_user_from_headers(
    x_user_id: Optional[str] = Header(None),
    x_user_email: Optional[str] = Header(None),
    x_authenticated: Optional[str] = Header(None)
):
    """
    Extract user information from headers set by API Gateway JWT middleware
    This dependency function ensures the user is authenticated via JWT
    """
    if not x_user_id or x_authenticated != "true":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "error": "authentication_required",
                "error_description": "This endpoint requires JWT authentication"
            }
        )
    
    return {
        "user_id": x_user_id,
        "email": x_user_email or "",
        "authenticated": True
    }

# Service dependency
def get_user_profile_service() -> UserProfileService:
    """
    Create and return UserProfileService instance
    """
    return create_user_profile_service(
        settings.SUPABASE_URL,
        settings.SUPABASE_SERVICE_ROLE_KEY
    )

@router.get("/me", response_model=UserProfileRead)
async def get_me(
    current_user: dict = Depends(get_current_user_from_headers),
    profile_service: UserProfileService = Depends(get_user_profile_service)
):
    """
    Get current user's profile information (JWT protected endpoint)
    
    This endpoint requires JWT authentication and returns the authenticated user's profile.
    If the profile doesn't exist, it will create a new one automatically.
    """
    try:
        # First, try to get existing profile
        profile, error = await profile_service.get_user_profile(current_user["user_id"])
        
        if profile:
            return profile
        
        # If profile doesn't exist, create a new one
        logger.info(f"Creating new profile for user {current_user['user_id']}")
        
        new_profile_data = UserProfileCreate(
            user_id=current_user["user_id"],
            full_name=None,  # Will be set later by user
            avatar_url=None,
            bio=None,
            university_name=None
        )
        
        profile, error = await profile_service.create_user_profile(new_profile_data)
        
        if profile:
            return profile
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": "profile_creation_failed",
                    "error_description": error or "Failed to create user profile"
                }
            )
            
    except Exception as e:
        logger.error(f"Error in get_me endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "internal_server_error",
                "error_description": "An error occurred while retrieving your profile"
            }
        )

@router.put("/me", response_model=UserProfileRead)
async def update_me(
    profile_data: UserProfileUpdate,
    current_user: dict = Depends(get_current_user_from_headers),
    profile_service: UserProfileService = Depends(get_user_profile_service)
):
    """
    Update current user's profile information (JWT protected endpoint)
    
    This endpoint allows the authenticated user to update their profile information.
    Only the fields provided in the request will be updated.
    """
    try:
        # Update the user's profile
        updated_profile, error = await profile_service.update_user_profile(
            current_user["user_id"], 
            profile_data
        )
        
        if updated_profile:
            return updated_profile
        else:
            # If profile doesn't exist, create it first
            if error and "not found" in error.lower():
                logger.info(f"Profile not found, creating new profile for user {current_user['user_id']}")
                
                new_profile_data = UserProfileCreate(
                    user_id=current_user["user_id"],
                    full_name=profile_data.full_name,
                    avatar_url=profile_data.avatar_url,
                    bio=profile_data.bio,
                    university_name=profile_data.university_name
                )
                
                created_profile, create_error = await profile_service.create_user_profile(new_profile_data)
                
                if created_profile:
                    return created_profile
                else:
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail={
                            "error": "profile_creation_failed",
                            "error_description": create_error or "Failed to create user profile"
                        }
                    )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "error": "profile_update_failed",
                        "error_description": error or "Failed to update user profile"
                    }
                )
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in update_me endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "internal_server_error",
                "error_description": "An error occurred while updating your profile"
            }
        )

@router.get("/{user_id}", response_model=UserProfilePublic)
async def get_user_profile(
    user_id: str,
    current_user: dict = Depends(get_current_user_from_headers),
    profile_service: UserProfileService = Depends(get_user_profile_service)
):
    """
    Get a user's profile by their user ID (JWT protected endpoint)
    
    This endpoint allows authenticated users to view other users' public profile information.
    Returns only public fields to protect user privacy.
    """
    try:
        # Get the requested user's profile
        profile, error = await profile_service.get_user_profile(user_id)
        
        if profile:
            # Return only public information
            public_profile = UserProfilePublic(
                id=profile.id,
                full_name=profile.full_name,
                avatar_url=profile.avatar_url,
                bio=profile.bio,
                university_name=profile.university_name
            )
            return public_profile
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "profile_not_found",
                    "error_description": f"Profile not found for user {user_id}"
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_user_profile endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "internal_server_error",
                "error_description": "An error occurred while retrieving the user profile"
            }
        )

@router.get("/me/preferences")
async def get_me_preferences(current_user: dict = Depends(get_current_user_from_headers)):
    """
    Get current user preferences (protected endpoint)
    """
    return {
        "message": "User preferences retrieved successfully",
        "userId": current_user["user_id"],
        "preferences": {
            "theme": "light",
            "language": "en",
            "notifications": {
                "email": True,
                "push": True,
                "in_app": True
            },
            "privacy": {
                "profile_visibility": "public",
                "learning_stats_visible": True
            },
            "learning": {
                "difficulty_level": "intermediate",
                "study_reminders": True,
                "auto_continue": False
            }
        },
        "timestamp": datetime.datetime.now().isoformat()
    }

@router.get("/health")
async def profiles_health():
    """
    Health check for profiles module
    """
    return {
        "status": "healthy",
        "module": "profiles",
        "timestamp": datetime.datetime.now().isoformat()
    }
