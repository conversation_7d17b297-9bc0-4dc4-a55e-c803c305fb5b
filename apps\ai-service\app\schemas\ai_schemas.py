"""
AI Service Schemas

This module defines Pydantic schemas for AI service requests and responses,
ensuring proper data validation and API documentation.
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class TeachingMode(str, Enum):
    """Teaching mode options for AI interactions."""
    SPOON_FEED = "spoon_feed"
    QUESTIONER = "questioner"
    SOCRATIC = "socratic"
    COLLABORATIVE = "collaborative"
    EXPLORATIVE = "explorative"

class ChatMessage(BaseModel):
    """Individual chat message."""
    role: str = Field(..., description="Message role (user, assistant, system)")
    content: str = Field(..., description="Message content")
    timestamp: Optional[datetime] = Field(default_factory=datetime.utcnow)

class ChatRequest(BaseModel):
    """Request schema for chat endpoint."""
    message: str = Field(..., description="User message", min_length=1, max_length=4000)
    context: Optional[List[ChatMessage]] = Field(default=[], description="Previous chat context")
    teaching_mode: Optional[TeachingMode] = Field(default=TeachingMode.COLLABORATIVE, description="Preferred teaching mode")
    subject: Optional[str] = Field(default=None, description="Subject context")
    user_id: Optional[str] = Field(default=None, description="User identifier")
    max_tokens: Optional[int] = Field(default=2000, description="Maximum tokens in response", ge=100, le=4000)
    temperature: Optional[float] = Field(default=0.7, description="Response creativity", ge=0.0, le=1.0)

class ChatResponse(BaseModel):
    """Response schema for chat endpoint."""
    response: str = Field(..., description="AI response")
    teaching_mode: TeachingMode = Field(..., description="Teaching mode used")
    confidence: float = Field(..., description="Response confidence", ge=0.0, le=1.0)
    sources: Optional[List[str]] = Field(default=[], description="Information sources")
    suggestions: Optional[List[str]] = Field(default=[], description="Follow-up suggestions")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    tokens_used: int = Field(..., description="Tokens consumed", ge=0)

class RAGRequest(BaseModel):
    """Request schema for RAG endpoint."""
    query: str = Field(..., description="Search query", min_length=1, max_length=1000)
    context: Optional[str] = Field(default=None, description="Additional context")
    subject_filter: Optional[str] = Field(default=None, description="Subject area filter")
    max_results: Optional[int] = Field(default=5, description="Maximum results to retrieve", ge=1, le=20)
    similarity_threshold: Optional[float] = Field(default=0.7, description="Similarity threshold", ge=0.0, le=1.0)
    user_id: Optional[str] = Field(default=None, description="User identifier")

class RAGSource(BaseModel):
    """Source information for RAG responses."""
    content: str = Field(..., description="Source content")
    title: Optional[str] = Field(default=None, description="Source title")
    source_type: str = Field(..., description="Type of source (document, note, etc.)")
    relevance_score: float = Field(..., description="Relevance score", ge=0.0, le=1.0)
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="Additional metadata")

class RAGResponse(BaseModel):
    """Response schema for RAG endpoint."""
    response: str = Field(..., description="Generated response")
    sources: List[RAGSource] = Field(..., description="Retrieved sources")
    query_expansion: Optional[List[str]] = Field(default=[], description="Expanded query terms")
    confidence: float = Field(..., description="Response confidence", ge=0.0, le=1.0)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    retrieval_time_ms: int = Field(..., description="Retrieval time in milliseconds", ge=0)

class SuggestionRequest(BaseModel):
    """Request schema for contextual suggestions."""
    context: str = Field(..., description="Current context", min_length=1, max_length=2000)
    user_query: Optional[str] = Field(default=None, description="User's current query")
    subject: Optional[str] = Field(default=None, description="Subject area")
    learning_level: Optional[str] = Field(default=None, description="Learning level (beginner, intermediate, advanced)")
    user_id: Optional[str] = Field(default=None, description="User identifier")
    max_suggestions: Optional[int] = Field(default=3, description="Maximum suggestions", ge=1, le=10)

class Suggestion(BaseModel):
    """Individual suggestion."""
    type: str = Field(..., description="Suggestion type (teaching_mode, query_refinement, learning_path)")
    content: str = Field(..., description="Suggestion content")
    confidence: float = Field(..., description="Suggestion confidence", ge=0.0, le=1.0)
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="Additional metadata")

class SuggestionResponse(BaseModel):
    """Response schema for contextual suggestions."""
    suggestions: List[Suggestion] = Field(..., description="Generated suggestions")
    context_analysis: Optional[Dict[str, Any]] = Field(default={}, description="Context analysis results")
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class ErrorResponse(BaseModel):
    """Error response schema."""
    error: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(default={}, description="Error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = Field(default=None, description="Request identifier")
