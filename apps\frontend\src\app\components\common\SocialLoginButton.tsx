"use client"

import React, { useState, useEffect } from "react"
import { SketchButton } from "./SketchButton"
import { signInWithOAuth } from "@/lib/supabaseClient"

interface SocialLoginButtonProps {
  provider: 'google' | 'github'
  mode?: 'signup' | 'login'
  className?: string
  disabled?: boolean
  onError?: (error: string) => void
  onLoadingChange?: (loading: boolean) => void
}

interface AuthState {
  isLoading: boolean
  error: string | null
  isRedirecting: boolean
}

const providerConfig = {
  google: {
    name: 'Google',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
        />
        <path
          fill="currentColor"
          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
        />
        <path
          fill="currentColor"
          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
        />
        <path
          fill="currentColor"
          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
        />
      </svg>
    ),
  },
  github: {
    name: 'GitHub',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
      </svg>
    ),
  },
}

export function SocialLoginButton({ 
  provider, 
  mode = 'login', 
  className,
  disabled = false,
  onError,
  onLoadingChange
}: SocialLoginButtonProps) {
  const [authState, setAuthState] = useState<AuthState>({
    isLoading: false,
    error: null,
    isRedirecting: false
  })
  
  const config = providerConfig[provider]
  
  // Clear error after 5 seconds
  useEffect(() => {
    if (authState.error) {
      const timer = setTimeout(() => {
        setAuthState(prev => ({ ...prev, error: null }))
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [authState.error])
  
  // Notify parent component of loading state changes
  useEffect(() => {
    onLoadingChange?.(authState.isLoading || authState.isRedirecting)
  }, [authState.isLoading, authState.isRedirecting, onLoadingChange])
  
  // Handle errors and provide user-friendly messages
  const handleError = (error: unknown, context: string) => {
    console.error(`${context}:`, error)
    
    let userFriendlyMessage = ''
    
    if (error instanceof Error) {
      const errorMessage = error.message.toLowerCase()
      
      // Map common OAuth errors to user-friendly messages
      switch (true) {
        case errorMessage.includes('popup'):
          userFriendlyMessage = 'Pop-up was blocked. Please allow pop-ups and try again.'
          break
        case errorMessage.includes('network'):
        case errorMessage.includes('fetch'):
          userFriendlyMessage = 'Network error. Please check your connection and try again.'
          break
        case errorMessage.includes('timeout'):
          userFriendlyMessage = 'Request timed out. Please try again.'
          break
        case errorMessage.includes('redirect'):
          userFriendlyMessage = 'Redirect configuration issue. Please contact support.'
          break
        case errorMessage.includes('unauthorized'):
          userFriendlyMessage = 'This application is not authorized. Please contact support.'
          break
        case errorMessage.includes('rate limit'):
        case errorMessage.includes('too many'):
          userFriendlyMessage = 'Too many attempts. Please wait a moment and try again.'
          break
        default:
          userFriendlyMessage = `Failed to connect with ${config.name}. Please try again.`
          break
      }
    } else {
      userFriendlyMessage = `An unexpected error occurred. Please try again.`
    }
    
    setAuthState(prev => ({ 
      ...prev, 
      error: userFriendlyMessage,
      isLoading: false,
      isRedirecting: false
    }))
    
    // Notify parent component of the error
    onError?.(userFriendlyMessage)
  }
  
  const validateRedirectURL = () => {
    try {
      const currentOrigin = window.location.origin
      const callbackURL = `${currentOrigin}/auth/callback`
      
      // Basic URL validation
      new URL(callbackURL)
      
      return callbackURL
    } catch (error) {
      console.error('Invalid redirect URL configuration:', error)
      throw new Error('Invalid redirect URL configuration')
    }
  }
  
  const handleSocialLogin = async () => {
    // Prevent multiple simultaneous attempts
    if (disabled || authState.isLoading || authState.isRedirecting) {
      return
    }
    
    // Clear any previous errors
    setAuthState(prev => ({ 
      ...prev, 
      error: null,
      isLoading: true
    }))
    
    try {
      // Validate redirect URL before attempting OAuth
      const redirectURL = validateRedirectURL()
      
      // Store current page for post-auth redirect if needed
      const currentPath = window.location.pathname
      if (currentPath !== '/login' && currentPath !== '/signup' && currentPath !== '/') {
        localStorage.setItem('auth_redirect_after_login', currentPath)
      }
      
      console.log(`Initiating ${provider} OAuth with redirect URL: ${redirectURL}`)
      
      const { error } = await signInWithOAuth(provider)
      
      if (error) {
        throw error
      }
      
      // If we reach here, the OAuth initiation was successful
      // Set redirecting state to show appropriate UI
      setAuthState(prev => ({ 
        ...prev, 
        isLoading: false,
        isRedirecting: true
      }))
      
      // The user will be redirected to the OAuth provider
      // and then back to our callback URL
      console.log(`OAuth initiation successful for ${provider}. Redirecting...`)
      
    } catch (error) {
      handleError(error, `Error initiating ${provider} OAuth`)
    }
  }
  
  // Determine button state and text
  const getButtonState = () => {
    if (authState.isRedirecting) {
      return {
        text: `Redirecting to ${config.name}...`,
        isDisabled: true,
        showSpinner: true
      }
    }
    
    if (authState.isLoading) {
      return {
        text: `Connecting to ${config.name}...`,
        isDisabled: true,
        showSpinner: true
      }
    }
    
    const baseText = mode === 'signup' 
      ? `Sign up with ${config.name}`
      : `Continue with ${config.name}`
    
    return {
      text: baseText,
      isDisabled: disabled,
      showSpinner: false
    }
  }
  
  const buttonState = getButtonState()
  
  return (
    <div className="w-full">
      <SketchButton
        onClick={handleSocialLogin}
        disabled={buttonState.isDisabled}
        variant="outline"
        className={`w-full flex items-center justify-center gap-3 py-3 transition-all duration-200 ${
          authState.error ? 'border-red-300 hover:border-red-400' : ''
        } ${className}`}
        aria-label={`${mode === 'signup' ? 'Sign up' : 'Sign in'} with ${config.name}`}
        type="button"
      >
        {buttonState.showSpinner ? (
          <div className="w-5 h-5 animate-spin rounded-full border-2 border-current border-t-transparent" 
               aria-hidden="true" />
        ) : (
          <span className="flex-shrink-0" aria-hidden="true">
            {config.icon}
          </span>
        )}
        <span className="font-medium truncate">
          {buttonState.text}
        </span>
      </SketchButton>
      
      {/* Error message display */}
      {authState.error && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md" 
             role="alert" 
             aria-live="polite">
          <div className="flex items-start gap-2">
            <svg className="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" 
                 fill="none" 
                 stroke="currentColor" 
                 viewBox="0 0 24 24"
                 aria-hidden="true">
              <path strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-red-700">
              {authState.error}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
