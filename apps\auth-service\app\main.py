"""
EVOLVE Auth Service - Main FastAPI application
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
import uvicorn
from contextlib import asynccontextmanager

from app.core.config import settings
from app.api.v1.endpoints import auth


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan context manager for FastAPI application"""
    # Startup
    print("🚀 EVOLVE Auth Service starting up...")
    yield
    # Shutdown
    print("🛑 EVOLVE Auth Service shutting down...")


# Initialize FastAPI app
app = FastAPI(
    title="EVOLVE Auth Service",
    description="Authentication and authorization service for EVOLVE platform",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix="/auth", tags=["authentication"])

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "auth-service"}


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "EVOLVE Auth Service",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8001,
        reload=True
    )
