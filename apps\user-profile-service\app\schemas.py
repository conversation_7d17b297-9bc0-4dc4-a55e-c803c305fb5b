"""
EVOLVE User Profile Service - Pydantic Schemas
"""

from pydantic import BaseModel, Field, HttpUrl
from typing import Optional
from datetime import datetime


class UserProfileCreate(BaseModel):
    """
    Schema for creating a new user profile.
    
    This schema includes only the fields that can be set when creating
    a new user profile. The user_id will typically be provided by the
    authentication system.
    """
    user_id: str = Field(..., description="User ID from auth system")
    full_name: Optional[str] = Field(None, max_length=255, description="User's full name")
    avatar_url: Optional[str] = Field(None, max_length=500, description="URL to user's avatar image")
    bio: Optional[str] = Field(None, description="User's biography or description")
    university_name: Optional[str] = Field(None, max_length=255, description="Name of user's university")
    
    class Config:
        """Pydantic configuration"""
        from_attributes = True
        json_schema_extra = {
            "example": {
                "user_id": "12345678-1234-1234-1234-123456789012",
                "full_name": "<PERSON>",
                "avatar_url": "https://example.com/avatar.jpg",
                "bio": "Software engineering student passionate about AI and web development.",
                "university_name": "University of Technology"
            }
        }


class UserProfileUpdate(BaseModel):
    """
    Schema for updating an existing user profile.
    
    All fields are optional to allow partial updates. Only the fields
    that are provided will be updated.
    """
    full_name: Optional[str] = Field(None, max_length=255, description="User's full name")
    avatar_url: Optional[str] = Field(None, max_length=500, description="URL to user's avatar image")
    bio: Optional[str] = Field(None, description="User's biography or description")
    university_name: Optional[str] = Field(None, max_length=255, description="Name of user's university")
    
    class Config:
        """Pydantic configuration"""
        from_attributes = True
        json_schema_extra = {
            "example": {
                "full_name": "John Smith",
                "bio": "Updated bio: Senior software engineering student with focus on machine learning.",
                "university_name": "MIT"
            }
        }


class UserProfileRead(BaseModel):
    """
    Schema for reading user profile information.
    
    This schema includes all fields that can be returned when reading
    a user profile, including metadata fields.
    """
    id: int = Field(..., description="Profile ID")
    user_id: str = Field(..., description="User ID from auth system")
    full_name: Optional[str] = Field(None, description="User's full name")
    avatar_url: Optional[str] = Field(None, description="URL to user's avatar image")
    bio: Optional[str] = Field(None, description="User's biography or description")
    university_name: Optional[str] = Field(None, description="Name of user's university")
    created_at: Optional[datetime] = Field(None, description="Profile creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Profile last update timestamp")
    
    class Config:
        """Pydantic configuration"""
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "user_id": "12345678-1234-1234-1234-123456789012",
                "full_name": "John Doe",
                "avatar_url": "https://example.com/avatar.jpg",
                "bio": "Software engineering student passionate about AI and web development.",
                "university_name": "University of Technology",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-20T14:45:00Z"
            }
        }


class UserProfilePublic(BaseModel):
    """
    Schema for public user profile information.
    
    This schema includes only the fields that should be publicly visible
    and excludes sensitive information.
    """
    id: int = Field(..., description="Profile ID")
    full_name: Optional[str] = Field(None, description="User's full name")
    avatar_url: Optional[str] = Field(None, description="URL to user's avatar image")
    bio: Optional[str] = Field(None, description="User's biography or description")
    university_name: Optional[str] = Field(None, description="Name of user's university")
    
    class Config:
        """Pydantic configuration"""
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "full_name": "John Doe",
                "avatar_url": "https://example.com/avatar.jpg",
                "bio": "Software engineering student passionate about AI and web development.",
                "university_name": "University of Technology"
            }
        }
