"""
App Configuration Settings

This module provides configuration settings for the EVOLVE AI Service, allowing
dynamic adjustment of environment-specific parameters such as host addresses,
database connections, and third-party services.

Configuration Source:
- Environment variables
- .env file in development
"""

from pydantic_settings import BaseSettings
from pydantic import ConfigDict
from typing import List

class Settings(BaseSettings):
    model_config = ConfigDict(env_file=".env", extra="ignore")
    
    ENVIRONMENT: str = "development"
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    
    # CORS settings
    ALLOWED_ORIGINS: List[str] = ["*"]
    
    # Trusted hosts settings
    TRUSTED_HOSTS: List[str] = ["*"]

# Create settings instance
settings = Settings()
