{"name": "@evolve/ai-service", "version": "1.0.0", "description": "EVOLVE AI Service - FastAPI backend for AI/ML operations", "private": true, "scripts": {"dev": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload", "start": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000", "ai:dev": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload", "ai:start": "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000", "test": "python -m pytest", "ai:test": "python -m pytest", "lint": "python -m flake8 app/", "format": "python -m black app/", "format:check": "python -m black --check app/", "type-check": "python -m mypy app/", "install": "pip install -r requirements.txt", "install:dev": "pip install -r requirements.txt && pip install -r requirements-dev.txt"}, "keywords": ["<PERSON><PERSON><PERSON>", "ai", "ml", "backend", "python"], "author": "", "license": "ISC"}