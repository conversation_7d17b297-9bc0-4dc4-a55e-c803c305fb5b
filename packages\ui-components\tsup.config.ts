import { defineConfig } from 'tsup'

export default defineConfig({
  entry: ['src/index.ts'],
  format: ['cjs', 'esm'],
  target: 'es2020',
  external: ['react', 'react-dom'],
  dts: true,
  sourcemap: true,
  clean: true,
  splitting: false,
  minify: false,
  banner: {
    js: `"use client";`,
  },
  esbuildOptions(options) {
    options.banner = {
      js: '"use client";',
    }
  },
  onSuccess: async () => {
    console.log('✅ UI Components built successfully')
  },
})
