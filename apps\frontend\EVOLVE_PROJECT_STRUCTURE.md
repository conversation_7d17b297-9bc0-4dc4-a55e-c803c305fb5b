# EVOLVE Project Structure - Complete Authentication System

## Project Root: `C:\AAJ\AAJ\Evolve\EVOLVE`

```
EVOLVE/
├── .env                           # Environment variables (development)
├── .env.example                   # Example environment configuration
├── .gitignore                     # Git ignore rules
├── package.json                   # Root package.json for workspace
├── pnpm-lock.yaml                 # Package lock file
├── pnpm-workspace.yaml            # PNPM workspace configuration
├── turbo.json                     # Turborepo configuration
├── README.md                      # Project documentation
├── docker-compose.yml             # Docker services configuration
├── DOCKER_README.md               # Docker setup instructions
├── BACKEND_STRUCTURE_REVIEW.md    # Backend architecture review
├── test_auth_flow.py              # Python authentication test script
│
├── .turbo/                        # Turborepo cache (excluded from VCS)
├── node_modules/                  # Project dependencies (excluded from VCS)
│
├── apps/                          # Application layer
│   ├── frontend/                  # Next.js Frontend Application
│   │   ├── package.json           # Frontend dependencies
│   │   ├── next.config.js         # Next.js configuration
│   │   ├── tailwind.config.js     # Tailwind CSS configuration
│   │   ├── tsconfig.json          # TypeScript configuration
│   │   ├── 
│   │   ├── src/
│   │   │   ├── app/               # App Router structure (Next.js 13+)
│   │   │   │   ├── layout.tsx     # Root layout with ToastProvider
│   │   │   │   ├── page.tsx       # Home page
│   │   │   │   ├── globals.css    # Global styles
│   │   │   │   │
│   │   │   │   ├── login/         # Authentication Pages
│   │   │   │   │   └── page.tsx   # Login page with email/OAuth
│   │   │   │   │
│   │   │   │   ├── signup/        # User Registration
│   │   │   │   │   └── page.tsx   # Signup page with email/OAuth
│   │   │   │   │
│   │   │   │   ├── auth/          # OAuth Callback Handling
│   │   │   │   │   └── callback/
│   │   │   │   │       └── page.tsx  # OAuth callback processor
│   │   │   │   │
│   │   │   │   ├── dashboard/     # Protected Routes
│   │   │   │   │   └── page.tsx   # Dashboard (requires auth)
│   │   │   │   │
│   │   │   │   ├── components/    # Reusable UI Components
│   │   │   │   │   ├── ui/        # Base UI Components
│   │   │   │   │   │   ├── toast.tsx          # Toast notification component
│   │   │   │   │   │   ├── toast-provider.tsx # Toast context provider
│   │   │   │   │   │   ├── input.tsx          # Form input component
│   │   │   │   │   │   ├── label.tsx          # Form label component
│   │   │   │   │   │   └── alert.tsx          # Alert component
│   │   │   │   │   │
│   │   │   │   │   └── common/    # Application-specific Components
│   │   │   │   │       ├── SketchButton.tsx        # Custom button
│   │   │   │   │       ├── SocialLoginButton.tsx   # OAuth login buttons
│   │   │   │   │       ├── SocialLoginButton.example.tsx
│   │   │   │   │       └── PaperCard.tsx           # Card component
│   │   │   │   │
│   │   │   │   ├── hooks/         # Custom React Hooks
│   │   │   │   │   └── useAuth.ts # Authentication state management
│   │   │   │   │
│   │   │   │   └── lib/           # Utility Libraries
│   │   │   │       └── utils.ts   # Utility functions (cn helper)
│   │   │   │
│   │   │   └── lib/               # Core Authentication Library
│   │   │       ├── auth.ts        # Main auth functions (login, signup, OAuth)
│   │   │       ├── authErrors.ts  # Error handling and retry logic
│   │   │       └── supabaseClient.ts  # Supabase client configuration
│   │   │
│   │   └── .next/                 # Next.js build output (excluded from VCS)
│   │
│   └── ai-service/                # Python AI Service
│       ├── package.json           # Service metadata
│       ├── requirements.txt       # Python dependencies
│       ├── README.md              # Service documentation
│       ├── .env                   # AI service environment
│       ├── .pytest_cache/         # Test cache
│       ├── logs/                  # Application logs
│       │   └── app.log
│       ├── venv/                  # Python virtual environment
│       ├── tests/                 # Unit tests
│       │   ├── __init__.py
│       │   └── test_main.py
│       └── app/                   # Application code
│           ├── __init__.py
│           ├── main.py            # FastAPI entry point
│           ├── api/               # API routes
│           │   ├── __init__.py
│           │   └── v1/
│           │       ├── __init__.py
│           │       ├── router.py
│           │       └── endpoints/
│           │           ├── __init__.py
│           │           ├── ai.py
│           │           └── health.py
│           ├── core/              # Core configurations
│           │   ├── __init__.py
│           │   ├── config.py
│           │   ├── exceptions.py
│           │   └── logging.py
│           ├── schemas/           # Pydantic models
│           │   ├── __init__.py
│           │   └── ai_schemas.py
│           ├── services/          # Business logic
│           │   ├── __init__.py
│           │   └── ai_service.py
│           ├── models/            # Database models (empty)
│           ├── db/                # Database utilities (empty)
│           └── utils/             # Utility functions (empty)
│
├── docs/                          # Project Documentation
│   └── [documentation files]
│
├── nginx/                         # Web Server Configuration
│   └── [nginx config files]
│
├── packages/                      # Shared Packages/Libraries
│   └── [shared utilities]
│
├── scripts/                       # Build and Deployment Scripts
│   └── [automation scripts]
│
└── services/                      # Additional Microservices
    └── [other services]
```

## Key Authentication Components Detail

### Frontend Authentication Files

#### Core Authentication Library (`src/lib/`)
- **`auth.ts`** - Main authentication functions
  - Email/password login and signup
  - OAuth URL generation  
  - Session management and token refresh
  - Unified auth state management
  - Supabase and API Gateway integration

- **`authErrors.ts`** - Comprehensive error handling
  - Error classification and user-friendly messages
  - Retry logic with exponential backoff
  - Network and OAuth error handling
  - Rate limiting and timeout management

- **`supabaseClient.ts`** - Supabase configuration
  - OAuth provider setup (Google, GitHub)
  - Session persistence configuration
  - Error handling for OAuth flows

#### Authentication Pages (`src/app/`)
- **`login/page.tsx`** - Login interface
  - Email/password form with validation
  - Google and GitHub OAuth buttons
  - Error display with retry options
  - Session management integration

- **`signup/page.tsx`** - Registration interface
  - User registration form
  - Password confirmation validation
  - OAuth signup options
  - Terms and privacy policy links

- **`auth/callback/page.tsx`** - OAuth callback handler
  - PKCE and implicit flow support
  - Error processing for OAuth failures
  - Session establishment
  - Redirect management

#### Authentication Components (`src/app/components/`)
- **`SocialLoginButton.tsx`** - OAuth login buttons
  - Provider-specific styling (Google, GitHub)
  - Loading states and error handling
  - Redirect URL validation
  - Pop-up blocking detection

- **`useAuth.ts`** - Authentication hook
  - State management for auth status
  - Login, signup, logout operations
  - Session refresh and validation
  - Error handling with retry logic
  - Toast notifications integration

#### UI Components (`src/app/components/ui/`)
- **`toast-provider.tsx`** - Toast notification system
  - Context provider for global notifications
  - Authentication-specific error messages
  - Success and retry notifications

## Authentication Flow Overview

### 1. Email/Password Authentication
```
User Input → Validation → API Call → Token Storage → Dashboard Redirect
```

### 2. OAuth Authentication Flow
```
Button Click → Provider Redirect → OAuth Consent → Callback Processing → Session Setup → Dashboard
```

### 3. Session Management
```
Auto-refresh → Validation → Error Handling → Re-authentication if needed
```

### 4. Protected Route Access
```
Route Request → Auth Check → Redirect to Login OR Allow Access
```

## Environment Configuration

### Required Environment Variables
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase anonymous key
- `NEXT_PUBLIC_API_GATEWAY_URL` - Backend API URL

## Development Workflow

1. **Frontend Development Server**: `npm run dev` (runs on localhost:3000)
2. **Backend Services**: Started via Docker Compose
3. **Authentication Testing**: Manual testing via UI + automated tests
4. **OAuth Configuration**: Google and GitHub OAuth apps configured
5. **Session Persistence**: LocalStorage + Supabase session management

## Testing Strategy

The authentication system is designed to be tested through:
- **Manual UI Testing**: Login/signup forms and OAuth flows
- **API Testing**: Direct backend authentication endpoints
- **Integration Testing**: Full flow from frontend to backend
- **Error Scenario Testing**: Network failures, invalid credentials, OAuth errors
- **Session Testing**: Token refresh, persistence, and expiration
