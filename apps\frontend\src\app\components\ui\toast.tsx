/**
 * Toast Notification System
 * Provides user-friendly notifications for authentication errors and success messages
 */

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from "lucide-react"
import { cn } from "@/app/lib/utils"

const toastVariants = cva(
  "group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-4 pr-8 shadow-lg transition-all paper-bg",
  {
    variants: {
      variant: {
        default: "border-border bg-background text-foreground",
        destructive: "border-red-500/50 bg-red-50 text-red-900 dark:border-red-500 dark:bg-red-950/20 dark:text-red-100",
        success: "border-green-500/50 bg-green-50 text-green-900 dark:border-green-500 dark:bg-green-950/20 dark:text-green-100",
        warning: "border-yellow-500/50 bg-yellow-50 text-yellow-900 dark:border-yellow-500 dark:bg-yellow-950/20 dark:text-yellow-100",
        info: "border-blue-500/50 bg-blue-50 text-blue-900 dark:border-blue-500 dark:bg-blue-950/20 dark:text-blue-100",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface ToastProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof toastVariants> {
  title?: string
  description?: string
  action?: React.ReactNode
  onClose?: () => void
  duration?: number
  showCloseButton?: boolean
  icon?: React.ReactNode
  retryable?: boolean
  onRetry?: () => void
  retryText?: string
}

const Toast = React.forwardRef<HTMLDivElement, ToastProps>(
  ({ 
    className, 
    variant, 
    title, 
    description, 
    action, 
    onClose, 
    duration = 5000,
    showCloseButton = true,
    icon,
    retryable = false,
    onRetry,
    retryText = "Try Again",
    ...props 
  }, ref) => {
    const [isVisible, setIsVisible] = React.useState(true)
    const [timeLeft, setTimeLeft] = React.useState(duration)

    React.useEffect(() => {
      if (duration > 0) {
        const timer = setTimeout(() => {
          setIsVisible(false)
          setTimeout(() => onClose?.(), 300)
        }, duration)

        const countdownTimer = setInterval(() => {
          setTimeLeft(prev => Math.max(0, prev - 100))
        }, 100)

        return () => {
          clearTimeout(timer)
          clearInterval(countdownTimer)
        }
      }
    }, [duration, onClose])

    const handleClose = () => {
      setIsVisible(false)
      setTimeout(() => onClose?.(), 300)
    }

    const getIcon = () => {
      if (icon) return icon
      
      switch (variant) {
        case 'success':
          return <CheckCircle className="h-5 w-5 text-green-600" />
        case 'destructive':
          return <AlertCircle className="h-5 w-5 text-red-600" />
        case 'warning':
          return <AlertTriangle className="h-5 w-5 text-yellow-600" />
        case 'info':
          return <Info className="h-5 w-5 text-blue-600" />
        default:
          return null
      }
    }

    const progressPercentage = duration > 0 ? (timeLeft / duration) * 100 : 0

    return (
      <div
        ref={ref}
        className={cn(
          toastVariants({ variant }),
          "transform transition-all duration-300 ease-in-out",
          isVisible ? "translate-x-0 opacity-100" : "translate-x-full opacity-0",
          className
        )}
        {...props}
      >
        <div className="flex items-start space-x-3 flex-1">
          {getIcon()}
          
          <div className="flex-1 space-y-1">
            {title && (
              <div className="font-semibold text-sm font-heading">
                {title}
              </div>
            )}
            {description && (
              <div className="text-sm font-sans opacity-90">
                {description}
              </div>
            )}
            
            {retryable && onRetry && (
              <div className="flex items-center space-x-2 mt-2">
                <button
                  onClick={onRetry}
                  className="text-xs px-2 py-1 rounded bg-primary/10 hover:bg-primary/20 text-primary font-medium transition-colors"
                >
                  {retryText}
                </button>
              </div>
            )}
          </div>
        </div>

        {action && (
          <div className="flex-shrink-0">
            {action}
          </div>
        )}

        {showCloseButton && (
          <button
            onClick={handleClose}
            className="absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100"
            aria-label="Close notification"
          >
            <X className="h-4 w-4" />
          </button>
        )}

        {duration > 0 && (
          <div className="absolute bottom-0 left-0 h-1 bg-primary/20 w-full">
            <div
              className="h-full bg-primary transition-all duration-100 ease-linear"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        )}
      </div>
    )
  }
)

Toast.displayName = "Toast"

export { Toast, toastVariants }
