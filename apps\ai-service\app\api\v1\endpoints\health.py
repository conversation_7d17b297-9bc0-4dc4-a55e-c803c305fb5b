"""
Health Check Endpoints

This module provides health check endpoints for monitoring the EVOLVE AI Service.
"""

from fastapi import APIRouter
from datetime import datetime
from typing import Dict, Any

router = APIRouter()

@router.get("/")
async def health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    
    Returns:
        Dict containing health status and basic service information.
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "EVOLVE AI Service",
        "version": "1.0.0"
    }

@router.get("/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """
    Detailed health check endpoint with component status.
    
    Returns:
        Dict containing detailed health status of all components.
    """
    # In a real implementation, you would check:
    # - Database connectivity
    # - External API availability (OpenAI, Google AI, etc.)
    # - Memory usage
    # - Disk space
    # - Any other critical dependencies
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "EVOLVE AI Service",
        "version": "1.0.0",
        "components": {
            "database": "healthy",
            "langchain": "healthy",
            "openai_api": "healthy",
            "google_ai": "healthy",
            "memory": "healthy",
            "storage": "healthy"
        },
        "uptime": "0h 0m 0s",  # Would be calculated from startup time
        "requests_served": 0   # Would be tracked by middleware
    }
