-- =============================================================================
-- EVOLVE Database Initialization Script
-- =============================================================================
-- This script initializes the EVOLVE database with basic setup
-- It runs automatically when the PostgreSQL container starts for the first time
-- =============================================================================

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create development user (if not already exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'evolve_dev') THEN
        CREATE USER evolve_dev WITH PASSWORD 'evolve_dev_password';
        GRANT ALL PRIVILEGES ON DATABASE evolve TO evolve_dev;
    END IF;
END
$$;

-- Create test database
SELECT 'CREATE DATABASE evolve_test'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'evolve_test')\gexec

-- Grant permissions for test database
GRANT ALL PRIVILEGES ON DATABASE evolve_test TO postgres;
GRANT ALL PRIVILEGES ON DATABASE evolve_test TO evolve_dev;

-- Basic table structure (placeholder - will be managed by migrations)
-- This is just for initial setup verification

-- Users table (basic structure)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for email lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Insert a test user for development
INSERT INTO users (email) VALUES ('<EMAIL>')
ON CONFLICT (email) DO NOTHING;

-- Log successful initialization
INSERT INTO users (email) VALUES ('<EMAIL>')
ON CONFLICT (email) DO NOTHING;

-- =============================================================================
-- Initialization Complete
-- =============================================================================
