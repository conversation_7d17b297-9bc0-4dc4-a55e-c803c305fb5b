@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
    
    /* EVOLVE Brand Variables */
    --evolve-blue: 210 100% 50%;
    --evolve-green: 134 61% 45%;
    --evolve-yellow: 45 100% 51%;
    --evolve-paper: 0 0% 99.6%;
    --evolve-ink: 0 0% 10.2%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* EVOLVE Paper Theme */
@layer components {
  .paper-texture {
    background-image: 
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.05) 0%, transparent 50%);
  }
  
  .line-art-border {
    border: 2px solid;
    border-image: url("data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M0,0 Q25,5 50,0 T100,0 L100,100 Q75,95 50,100 T0,100 Z' stroke='%23000' stroke-width='2' fill='none'/%3e%3c/svg%3e") 2;
  }
  
  .notebook-margin {
    border-left: 2px solid #ff6b6b;
    padding-left: 1rem;
    margin-left: 2rem;
  }
  
  .sketch-shadow {
    box-shadow: 
      0 1px 3px rgba(0, 0, 0, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.05),
      inset 0 0 0 1px rgba(0, 0, 0, 0.05);
  }
}

/* Scribble animations */
@keyframes scribble {
  0%, 100% { transform: translateX(0) rotate(0deg); }
  25% { transform: translateX(-1px) rotate(-0.5deg); }
  50% { transform: translateX(1px) rotate(0.5deg); }
  75% { transform: translateX(-0.5px) rotate(-0.25deg); }
}

@layer utilities {
  .animate-scribble {
    animation: scribble 0.3s ease-in-out;
  }
  
  .torn-edge {
    clip-path: polygon(
      0% 0%, 
      95% 0%, 
      100% 5%, 
      100% 95%, 
      95% 100%, 
      5% 100%, 
      0% 95%
    );
  }
}
