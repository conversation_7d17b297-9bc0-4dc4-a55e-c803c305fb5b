"""
Authentication endpoints for EVOLVE Auth Service
"""

import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from gotrue.errors import AuthApiError

from app.schemas.auth import (
    PasswordResetRequest,
    PasswordUpdateRequest,
    PasswordResetResponse,
    LoginRequest,
    LoginResponse,
    ErrorResponse,
    OAuthURLResponse,
    RefreshTokenRequest,
    TokenResponse,
    LogoutRequest,
    EmailVerificationResponse,
    SignUpRequest,
    SignUpResponse
)
from app.services.supabase_service import supabase_service
from app.core.config import settings
from app.utils.jwt import jwt_manager


# Create authentication router
router = APIRouter()

logger = logging.getLogger(__name__)


@router.post("/login", response_model=LoginResponse, responses={
    status.HTTP_400_BAD_REQUEST: {
        "model": ErrorResponse,
        "description": "Invalid credentials"
    },
    status.HTTP_500_INTERNAL_SERVER_ERROR: {
        "model": ErrorResponse,
        "description": "Internal server error"
    }
})
async def login(login_request: LoginRequest) -> LoginResponse:
    """
    User login endpoint with email and password

    Args:
        login_request: Login request containing user credentials

    Returns:
        LoginResponse with access token and user information

    Raises:
        HTTPException - In case of invalid credentials or server errors
    """
    logger.info(f"Attempting login for user: {login_request.email}")

    # Attempt to login user
    user_data, error = await supabase_service.sign_in_with_password(
        email=login_request.email,
        password=login_request.password
    )

    # Handle login error
    if error:
        logger.error(f"Login failed for user: {login_request.email}, error: {error}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "invalid_credentials", "error_description": error}
        )

    if user_data and user_data.get("session") and user_data.get("user"):
        # Create JWT tokens
        access_token = jwt_manager.create_access_token(subject=user_data["user"]["id"])
        refresh_token = jwt_manager.create_refresh_token(subject=user_data["user"]["id"])
        
        logger.info(f"Login successful for user: {login_request.email}")
        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user_data["user"]
        )

    logger.error(f"Unexpected error occurred during login for user: {login_request.email}")
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail={"error": "server_error", "error_description": "Unexpected error occurred"}
    )


@router.get("/oauth/{provider}", response_model=OAuthURLResponse, responses={
    status.HTTP_400_BAD_REQUEST: {
        "model": ErrorResponse,
        "description": "Invalid provider"
    }
})
async def get_oauth_url(provider: str) -> OAuthURLResponse:
    """
    Get OAuth authorization URL

    Args:
        provider: OAuth provider (google, github)

    Returns:
        OAuthURLResponse with authorization URL

    Raises:
        HTTPException - For invalid provider or URL retrieval
    """
    logger.info(f"Getting OAuth URL for provider: {provider}")

    if provider not in ["google", "github"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "invalid_provider", "error_description": "Supported providers: google, github"}
        )

    redirect_url = f"{settings.FRONTEND_URL}/oauth/callback/{provider}"
    auth_url, error = await supabase_service.get_oauth_url(provider, redirect_url)

    if error:
        logger.error(f"Error retrieving OAuth URL: {error}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "oauth_error", "error_description": error}
        )

    return OAuthURLResponse(provider=provider, auth_url=auth_url)


@router.post("/refresh-token", response_model=TokenResponse, responses={
    status.HTTP_400_BAD_REQUEST: {
        "model": ErrorResponse,
        "description": "Invalid or expired refresh token"
    },
    status.HTTP_401_UNAUTHORIZED: {
        "model": ErrorResponse,
        "description": "Refresh token expired or invalid"
    },
    status.HTTP_500_INTERNAL_SERVER_ERROR: {
        "model": ErrorResponse,
        "description": "Internal server error"
    }
})
async def refresh_token(refresh_request: RefreshTokenRequest) -> TokenResponse:
    """
    Refresh access token using refresh token

    Args:
        refresh_request: Refresh token request containing the refresh token

    Returns:
        TokenResponse with new access token and refresh token

    Raises:
        HTTPException - In case of invalid/expired refresh token or server errors
    """
    logger.info("Attempting to refresh access token")

    # Verify refresh token
    user_id = jwt_manager.verify_refresh_token(refresh_request.refresh_token)
    if not user_id:
        logger.error("Invalid refresh token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "error": "invalid_refresh_token", 
                "error_description": "Refresh token is expired or invalid"
            }
        )

    # Create new access token
    new_access_token = jwt_manager.create_access_token(subject=user_id)

    logger.info("Token refresh successful")
    return TokenResponse(
        access_token=new_access_token,
        token_type="bearer",
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/logout", responses={
    status.HTTP_200_OK: {
        "description": "Successfully logged out"
    },
    status.HTTP_400_BAD_REQUEST: {
        "model": ErrorResponse,
        "description": "Invalid access token"
    },
    status.HTTP_500_INTERNAL_SERVER_ERROR: {
        "model": ErrorResponse,
        "description": "Internal server error"
    }
})
async def logout(logout_request: LogoutRequest) -> dict:
    """
    User logout endpoint

    Args:
        logout_request: Logout request containing the access token

    Returns:
        Success message

    Raises:
        HTTPException - In case of invalid access token or server errors
    """
    logger.info("Attempting to logout user")

    # Attempt to sign out user
    success, error = await supabase_service.sign_out(
        access_token=logout_request.access_token
    )

    if error:
        logger.error(f"Logout failed, error: {error}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "logout_error", "error_description": error}
        )

    if success:
        logger.info("Logout successful")
        return {"message": "Successfully logged out"}

    logger.error("Unexpected error occurred during logout")
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail={"error": "server_error", "error_description": "Unexpected error occurred during logout"}
    )



@router.post("/request-password-reset", response_model=PasswordResetResponse, responses={
    status.HTTP_200_OK: {
        "description": "Password reset email sent"
    },
    status.HTTP_400_BAD_REQUEST: {
        "model": ErrorResponse,
        "description": "Invalid email"
    },
    status.HTTP_500_INTERNAL_SERVER_ERROR: {
        "model": ErrorResponse,
        "description": "Internal server error"
    }
})
async def request_password_reset(
    password_reset_request: PasswordResetRequest) -> PasswordResetResponse:
    """
    Request password reset endpoint

    Args:
        password_reset_request: Request containing user email

    Returns:
        Message confirming password reset email sent

    Raises:
        HTTPException - On failure
    """
    logger.info("Attempting to send password reset email")

    try:
        # Send password reset email
        success, error = await supabase_service.reset_password_for_email(
            email=password_reset_request.email
        )
        if not success:
            logger.error(f"Password reset email error: {error}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"error": "reset_failed", "error_description": error}
            )

        return PasswordResetResponse(
            message="If your email is registered, you will receive a password reset link"
        )

    except Exception as e:
        logger.error(f"Unexpected error during password reset request: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "server_error", "error_description": "Unexpected error occurred"}
        )


@router.post("/update-password", response_model=PasswordResetResponse, responses={
    status.HTTP_200_OK: {
        "description": "Password updated successfully"
    },
    status.HTTP_400_BAD_REQUEST: {
        "model": ErrorResponse,
        "description": "Invalid access token or new password"
    },
    status.HTTP_500_INTERNAL_SERVER_ERROR: {
        "model": ErrorResponse,
        "description": "Internal server error"
    }
})
async def update_password(
    access_token: str,
    new_password: str
) -> PasswordResetResponse:
    """
    Update password endpoint

    Args:
        access_token: Access token from password reset link
        new_password: New password for the user

    Returns:
        Message confirming password update

    Raises:
        HTTPException - On failure
    """
    logger.info("Attempting to update password")

    try:
        # Update password
        user_data, error = await supabase_service.update_user_password(
            access_token=access_token,
            new_password=new_password
        )
        if error:
            logger.error(f"Password update error: {error}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"error": "update_failed", "error_description": error}
            )

        logger.info("Password updated successfully")
        return PasswordResetResponse(message="Password updated successfully. Please login with your new password.")

    except Exception as e:
        logger.error(f"Unexpected error during password update: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "server_error", "error_description": "Unexpected error occurred"}
        )


@router.post("/signup", response_model=SignUpResponse, responses={
    status.HTTP_400_BAD_REQUEST: {
        "model": ErrorResponse,
        "description": "Invalid input"
    },
    status.HTTP_500_INTERNAL_SERVER_ERROR: {
        "model": ErrorResponse,
        "description": "Internal server error"
    }
})
async def sign_up(sign_up_request: SignUpRequest) -> SignUpResponse:
    """
    User sign-up endpoint

    Args:
        sign_up_request: SignUp request containing user details

    Returns:
        SignUpResponse with user information and session

    Raises:
        HTTPException - For sign-up failure
    """
    logger.info("Attempting to sign up user")

    # Attempt to sign up user
    user_data, error = await supabase_service.sign_up(
        email=sign_up_request.email,
        password=sign_up_request.password,
        options={"data": sign_up_request.metadata}
    )

    # Handle sign-up error
    if error:
        logger.error(f"Sign up failed, error: {error}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "signup_failed", "error_description": error}
        )

    if user_data:
        return SignUpResponse(
            message="User signed up successfully.",
            user=user_data.get("user"),
            session=user_data.get("session"),
            confirmation_sent=user_data.get("confirmation_sent")
        )

    logger.error("Unexpected error occurred during sign up")
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail={"error": "server_error", "error_description": "Unexpected error occurred"}
    )


@router.get("/verify-email", response_model=EmailVerificationResponse, responses={
    status.HTTP_400_BAD_REQUEST: {
        "model": ErrorResponse,
        "description": "Invalid or expired tokens"
    },
    status.HTTP_500_INTERNAL_SERVER_ERROR: {
        "model": ErrorResponse,
        "description": "Internal server error"
    }
})
async def verify_email(
    access_token: str,
    refresh_token: str,
    token_type: Optional[str] = "bearer",
    expires_in: Optional[int] = None,
    expires_at: Optional[int] = None,
    type: Optional[str] = None,
) -> EmailVerificationResponse:
    """
    Email verification endpoint

    Args:
        access_token: Access token from verification email
        refresh_token: Refresh token from verification email
        token_type: Token type (optional)
        expires_in: Token expiration time (optional)
        expires_at: Token expiration timestamp (optional)
        type: Verification type (optional)

    Returns:
        Email verification confirmation

    Raises:
        HTTPException - On failure or bad request
    """
    logger.info("Attempting email verification")

    try:
        # Set session
        supabase_service.supabase.auth.set_session(
            access_token=access_token,
            refresh_token=refresh_token
        )

        # Get user info
        user_info, error = await supabase_service.get_user_info(access_token)

        if error:
            logger.error(f"Verification failed, error: {error}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"error": "verification_failed", "error_description": error}
            )

        if user_info and user_info.get("email_verified"):
            redirect_url = settings.FRONTEND_URL + "/dashboard"
            return EmailVerificationResponse(
                message="Email successfully verified.",
                access_token=access_token,
                refresh_token=refresh_token,
                token_type=token_type,
                expires_in=expires_in or 0,
                user=user_info,
                redirect_url=redirect_url
            )

    except Exception as e:
        logger.error(f"Unexpected error occurred during email verification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "server_error", "error_description": "Unexpected error occurred"}
        )
