{"name": "@evolve/shared-types", "version": "1.0.0", "description": "Shared TypeScript type definitions for EVOLVE platform", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "src"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf dist .turbo node_modules"}, "devDependencies": {"tsup": "^8.0.2", "typescript": "^5.3.3"}, "keywords": ["typescript", "types", "definitions", "shared"], "author": "EVOLVE Team", "license": "MIT", "publishConfig": {"access": "restricted"}}