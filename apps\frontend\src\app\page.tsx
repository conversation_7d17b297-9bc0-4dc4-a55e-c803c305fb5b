'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { getAuthState, isSessionValid, type AuthState } from '@/lib/auth'
import { Logo } from '@/app/components/common/Logo'

export default function HomePage() {
  const router = useRouter()
  const [authState, setAuthState] = useState<AuthState | null>(null)
  const [isChecking, setIsChecking] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Navigation flags to prevent multiple simultaneous redirects
  const isNavigating = useRef(false)
  const hasNavigated = useRef(false)
  
  // Debouncing state
  const debounceTimer = useRef<NodeJS.Timeout | null>(null)
  const abortController = useRef<AbortController | null>(null)

  const checkAuthenticationState = useCallback(async () => {
    // Prevent multiple simultaneous navigation attempts
    if (isNavigating.current || hasNavigated.current) {
      return
    }

    try {
      // Create new abort controller for this check
      abortController.current = new AbortController()
      
      setIsChecking(true)
      setError(null)
      
      // Get comprehensive auth state
      const currentAuthState = await getAuthState()
      
      // Check if the component was unmounted or a new check started
      if (abortController.current?.signal.aborted) {
        return
      }
      
      setAuthState(currentAuthState)
      
      if (currentAuthState.isAuthenticated) {
        // Validate the session to ensure it's still valid
        const isValid = await isSessionValid()
        
        // Check again if the component was unmounted or a new check started
        if (abortController.current?.signal.aborted) {
          return
        }
        
        // Set navigation flag to prevent multiple redirects
        isNavigating.current = true
        hasNavigated.current = true
        
        if (isValid) {
          // Session is valid, redirect to dashboard
          router.replace('/dashboard')
        } else {
          // Session is invalid, redirect to login
          router.replace('/login?error=session_expired')
        }
      } else {
        // Set navigation flag to prevent multiple redirects
        isNavigating.current = true
        hasNavigated.current = true
        
        // Not authenticated, redirect to login
        router.replace('/login')
      }
    } catch (err) {
      // Check if the error is due to abortion
      if (abortController.current?.signal.aborted) {
        return
      }
      
      console.error('Error checking authentication state:', err)
      setError('Failed to check authentication status')
      
      // On error, redirect to login after a short delay
      setTimeout(() => {
        if (!abortController.current?.signal.aborted && !hasNavigated.current) {
          isNavigating.current = true
          hasNavigated.current = true
          router.replace('/login?error=auth_check_failed')
        }
      }, 2000)
    } finally {
      if (!abortController.current?.signal.aborted) {
        setIsChecking(false)
      }
    }
  }, [router])

  useEffect(() => {
    // Clear any existing debounce timer
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current)
    }
    
    // Debounce the authentication check to prevent rapid successive calls
    debounceTimer.current = setTimeout(() => {
      checkAuthenticationState()
    }, 100) // 100ms debounce

    // Cleanup function
    return () => {
      // Clear debounce timer
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current)
      }
      
      // Abort any ongoing authentication check
      if (abortController.current) {
        abortController.current.abort()
      }
    }
  }, [checkAuthenticationState])

  // Show loading state while checking authentication
  if (isChecking) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-background">
        <div className="text-center space-y-4">
          <div className="mb-4">
            <Logo size="lg" showText={true} className="justify-center" />
          </div>
          <div className="w-8 h-8 mx-auto animate-spin rounded-full border-2 border-primary border-t-transparent" />
          <p className="text-muted-foreground font-sans">Checking authentication...</p>
          {authState && (
            <p className="text-sm text-muted-foreground font-sans">
              Authentication method: {authState.authMethod || 'detecting'}
            </p>
          )}
        </div>
      </div>
    )
  }

  // Show error state if authentication check failed
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-background">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 mx-auto rounded-full bg-red-100 flex items-center justify-center">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h2 className="text-xl font-heading text-red-600">Authentication Error</h2>
          <p className="text-muted-foreground font-sans">{error}</p>
          <p className="text-sm text-muted-foreground font-sans">Redirecting to login...</p>
        </div>
      </div>
    )
  }

  // Fallback loading state
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background">
        <div className="text-center space-y-4">
          <div className="mb-4">
            <Logo size="lg" showText={true} className="justify-center" />
          </div>
          <div className="w-8 h-8 mx-auto animate-spin rounded-full border-2 border-primary border-t-transparent" />
          <p className="text-muted-foreground font-sans">Redirecting...</p>
      </div>
    </div>
  )
}
