#!/usr/bin/env python3
"""
EVOLVE Authentication Flow Test Script

This script tests the end-to-end authentication flow:
1. Register/Login through Auth Service via API Gateway
2. Use returned JWT token to access protected User Profile Service endpoints
"""

import requests
import json
import time
import sys

# Configuration
API_GATEWAY_URL = "http://localhost:3001"
AUTH_SERVICE_URL = "http://localhost:8001"
USER_SERVICE_URL = "http://localhost:8003"

# Test user credentials
TEST_USER = {
    "email": "<EMAIL>",
    "password": "testpassword123",
    "full_name": "Test User"
}

class AuthFlowTester:
    def __init__(self):
        self.access_token = None
        self.refresh_token = None
        self.user_info = None

    def test_direct_auth_service(self):
        """Test direct connection to auth service"""
        print("\n🔍 Testing direct connection to Auth Service...")
        
        try:
            response = requests.get(f"{AUTH_SERVICE_URL}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Auth Service is running")
                return True
            else:
                print(f"❌ Auth Service returned status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Cannot connect to Auth Service: {e}")
            return False

    def test_direct_user_service(self):
        """Test direct connection to user profile service"""
        print("\n🔍 Testing direct connection to User Profile Service...")
        
        try:
            response = requests.get(f"{USER_SERVICE_URL}/health", timeout=5)
            if response.status_code == 200:
                print("✅ User Profile Service is running")
                return True
            else:
                print(f"❌ User Profile Service returned status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Cannot connect to User Profile Service: {e}")
            return False

    def test_api_gateway_health(self):
        """Test API Gateway health"""
        print("\n🔍 Testing API Gateway connection...")
        
        try:
            response = requests.get(f"{API_GATEWAY_URL}/health", timeout=5)
            if response.status_code == 200:
                print("✅ API Gateway is running")
                return True
            else:
                print(f"❌ API Gateway returned status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Cannot connect to API Gateway: {e}")
            return False

    def test_user_registration(self):
        """Test user registration through API Gateway"""
        print("\n📝 Testing user registration via API Gateway...")
        
        try:
            response = requests.post(
                f"{API_GATEWAY_URL}/api/auth/signup",
                json={
                    "email": TEST_USER["email"],
                    "password": TEST_USER["password"],
                    "full_name": TEST_USER["full_name"]
                },
                timeout=10
            )
            
            print(f"Response Status: {response.status_code}")
            print(f"Response Body: {response.text}")
            
            if response.status_code in [200, 201]:
                print("✅ User registration successful")
                return True
            elif response.status_code == 400 and "already exists" in response.text.lower():
                print("ℹ️ User already exists, proceeding to login")
                return True
            else:
                print(f"❌ Registration failed with status {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Registration request failed: {e}")
            return False

    def test_user_login(self):
        """Test user login through API Gateway"""
        print("\n🔐 Testing user login via API Gateway...")
        
        try:
            response = requests.post(
                f"{API_GATEWAY_URL}/api/auth/login",
                json={
                    "email": TEST_USER["email"],
                    "password": TEST_USER["password"]
                },
                timeout=10
            )
            
            print(f"Response Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                self.refresh_token = data.get("refresh_token")
                self.user_info = data.get("user")
                
                print("✅ Login successful")
                print(f"   Access Token: {self.access_token[:50]}...")
                print(f"   User ID: {self.user_info.get('id') if self.user_info else 'N/A'}")
                return True
            else:
                print(f"❌ Login failed with status {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Login request failed: {e}")
            return False

    def test_protected_endpoint_without_token(self):
        """Test accessing protected endpoint without token"""
        print("\n🚫 Testing protected endpoint without authentication...")
        
        try:
            response = requests.get(f"{API_GATEWAY_URL}/api/user/profile", timeout=5)
            
            print(f"Response Status: {response.status_code}")
            
            if response.status_code == 401:
                print("✅ Correctly rejected unauthenticated request")
                return True
            else:
                print(f"❌ Expected 401, got {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
            return False

    def test_protected_endpoint_with_token(self):
        """Test accessing protected endpoint with valid token"""
        print("\n🔓 Testing protected endpoint with authentication...")
        
        if not self.access_token:
            print("❌ No access token available")
            return False
        
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{API_GATEWAY_URL}/api/profiles/me",
                headers=headers,
                timeout=10
            )
            
            print(f"Response Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Successfully accessed protected /profiles/me endpoint")
                print(f"   User ID from response: {data.get('userId', 'N/A')}")
                print(f"   Message: {data.get('message', 'N/A')}")
                return True
            else:
                print(f"❌ Failed to access protected endpoint: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
            return False

    def test_profiles_me_endpoint_without_token(self):
        """Test accessing /profiles/me endpoint without token"""
        print("\n🚫 Testing /profiles/me endpoint without authentication...")
        
        try:
            response = requests.get(f"{API_GATEWAY_URL}/api/profiles/me", timeout=5)
            
            print(f"Response Status: {response.status_code}")
            
            if response.status_code == 401:
                print("✅ Correctly rejected unauthenticated request to /profiles/me")
                return True
            else:
                print(f"❌ Expected 401, got {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
            return False

    def test_token_refresh(self):
        """Test token refresh functionality"""
        print("\n🔄 Testing token refresh...")
        
        if not self.refresh_token:
            print("❌ No refresh token available")
            return False
        
        try:
            response = requests.post(
                f"{API_GATEWAY_URL}/api/auth/refresh-token",
                json={"refresh_token": self.refresh_token},
                timeout=10
            )
            
            print(f"Response Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                new_access_token = data.get("access_token")
                
                if new_access_token:
                    print("✅ Token refresh successful")
                    print(f"   New Access Token: {new_access_token[:50]}...")
                    self.access_token = new_access_token
                    return True
                else:
                    print("❌ No access token in refresh response")
                    return False
            else:
                print(f"❌ Token refresh failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Refresh request failed: {e}")
            return False

    def run_full_test(self):
        """Run the complete authentication flow test"""
        print("🚀 Starting EVOLVE Authentication Flow Test")
        print("=" * 60)
        
        tests = [
            ("API Gateway Health", self.test_api_gateway_health),
            ("Auth Service Connection", self.test_direct_auth_service),
            ("User Profile Service Connection", self.test_direct_user_service),
            ("User Registration", self.test_user_registration),
            ("User Login", self.test_user_login),
            ("Unauth /profiles/me Access", self.test_profiles_me_endpoint_without_token),
            ("Auth /profiles/me Access", self.test_protected_endpoint_with_token),
            ("Token Refresh", self.test_token_refresh),
            ("Auth /profiles/me Access (Refreshed)", self.test_protected_endpoint_with_token),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            print(f"\n{'=' * 60}")
            try:
                result = test_func()
                results.append((test_name, result))
                if result:
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"❌ {test_name}: FAILED with exception: {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name:<30} {status}")
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Authentication system is working correctly.")
            return True
        else:
            print("⚠️ Some tests failed. Please check the service logs.")
            return False

if __name__ == "__main__":
    tester = AuthFlowTester()
    success = tester.run_full_test()
    sys.exit(0 if success else 1)
