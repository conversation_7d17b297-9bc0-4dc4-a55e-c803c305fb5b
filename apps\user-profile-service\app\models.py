"""
EVOLVE User Profile Service - SQLModel Models
"""

from sqlmodel import SQLModel, Field, Column, Text
from typing import Optional
from datetime import datetime
import uuid


class UserProfile(SQLModel, table=True):
    """
    User Profile model for storing user profile information.
    
    This model includes optional fields for user profile data and maintains
    a foreign key relationship to the auth.users table.
    """
    __tablename__ = "user_profiles"
    
    # Primary key
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # Foreign key to auth.users (UUID type commonly used in Supabase auth)
    user_id: str = Field(foreign_key="auth.users.id", unique=True, index=True)
    
    # Profile fields - all optional to allow flexible profile creation
    full_name: Optional[str] = Field(default=None, max_length=255)
    avatar_url: Optional[str] = Field(default=None, max_length=500)
    bio: Optional[str] = Field(default=None, sa_column=Column(Text))
    university_name: Optional[str] = Field(default=None, max_length=255)
    
    # Metadata fields
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    class Config:
        """SQLModel configuration"""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
