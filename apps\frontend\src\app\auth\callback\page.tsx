"use client"

import React, { useEffect, useState, Suspense } from "react"
import { useRouter } from "next/navigation"
import { supabase } from "@/lib/supabaseClient"
import { storeAuthData } from "@/lib/auth"
import { Logo } from "@/app/components/common/Logo"

function AuthCallbackComponent() {
  const router = useRouter()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')
  const [countdown, setCountdown] = useState(3)

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Check URL parameters for auth code (PKCE flow) or errors
        const urlParams = new URLSearchParams(window.location.search)
        const authCode = urlParams.get('code')
        const urlError = urlParams.get('error')
        const urlErrorDescription = urlParams.get('error_description')
        
        let session = null
        let sessionError = null
        
        if (authCode) {
          // PKCE flow: exchange code for session
          const { data, error } = await supabase.auth.exchangeCodeForSession(authCode)
          session = data.session
          sessionError = error
        } else {
          // Implicit flow: get session from URL fragment or existing session
          const { data, error } = await supabase.auth.getSession()
          session = data.session
          sessionError = error
        }
        
        if (urlError || sessionError) {
const errorType = sessionError ? 'Session Error' : 'URL Error'
          console.error(`Auth callback ${errorType}:`, sessionError || urlError)
          setStatus('error')

          const errorMessage = urlErrorDescription || sessionError?.message || urlError
          
          // Handle specific OAuth and authentication errors
          let userFriendlyMessage = ''
          switch (true) {
            case errorMessage?.includes('access_denied'):
              userFriendlyMessage = 'Access was denied. Please try again and grant the necessary permissions.'
              break
            case errorMessage?.includes('invalid_request'):
              userFriendlyMessage = 'Invalid authentication request. Please check your login method and try again.'
              break
            case errorMessage?.includes('invalid_grant'):
              userFriendlyMessage = 'Authentication code expired or invalid. Please try logging in again.'
              break
            case errorMessage?.includes('unauthorized_client'):
              userFriendlyMessage = 'This application is not authorized. Please contact support.'
              break
            case errorMessage?.includes('server_error'):
              userFriendlyMessage = 'Authentication server error. Please try again later.'
              break
            case errorMessage?.includes('temporarily_unavailable'):
              userFriendlyMessage = 'Authentication service is temporarily unavailable. Please try again later.'
              break
            case sessionError?.message?.includes('refresh_token_not_found'):
              userFriendlyMessage = 'Session expired. Please log in again.'
              break
            case sessionError?.message?.includes('invalid_token'):
              userFriendlyMessage = 'Invalid authentication token. Please log in again.'
              break
            default:
              userFriendlyMessage = `Authentication failed: ${errorMessage || 'Unknown error'}. Please try again.`
              break
          }
          
          setMessage(userFriendlyMessage)

          // Start countdown timer for error redirect
          const timer = setInterval(() => {
            setCountdown(prev => {
              if (prev <= 1) {
                clearInterval(timer)
                router.push('/login?error=auth_failed')
                return 0
              }
              return prev - 1
            })
          }, 1000)
          
          return
        }

        if (session) {
          // Store the authentication data in our format
          const authData = {
            access_token: session.access_token,
            refresh_token: session.refresh_token,
            token_type: 'bearer',
            expires_in: session.expires_in || 3600,
            user: {
              id: session.user.id,
              email: session.user.email || '',
              email_verified: session.user.email_confirmed_at ? true : false,
              created_at: session.user.created_at,
              updated_at: session.user.updated_at,
              user_metadata: session.user.user_metadata
            }
          }

          storeAuthData(authData)
          setStatus('success')
          
          // Check for intended redirect destination
          const redirectTo = urlParams.get('redirect_to') || 
                           localStorage.getItem('auth_redirect_after_login') || 
                           '/dashboard'
          
          // Clear stored redirect if it exists
          localStorage.removeItem('auth_redirect_after_login')
          
          setMessage(`Authentication successful! Redirecting to ${redirectTo === '/dashboard' ? 'dashboard' : 'your intended page'}...`)

          // Redirect to intended destination or dashboard
          setTimeout(() => {
            router.push(redirectTo)
          }, 2000)
        } else {
          // Wait a moment for session to be processed, then check again
          setTimeout(async () => {
            const { data: { session: retrySession } } = await supabase.auth.getSession()
            if (retrySession) {
              // Session found on retry, process it
              const authData = {
                access_token: retrySession.access_token,
                refresh_token: retrySession.refresh_token,
                token_type: 'bearer',
                expires_in: retrySession.expires_in || 3600,
                user: {
                  id: retrySession.user.id,
                  email: retrySession.user.email || '',
                  email_verified: retrySession.user.email_confirmed_at ? true : false,
                  created_at: retrySession.user.created_at,
                  updated_at: retrySession.user.updated_at,
                  user_metadata: retrySession.user.user_metadata
                }
              }
              
              storeAuthData(authData)
              setStatus('success')
              setMessage('Authentication successful! Redirecting to dashboard...')
              
              setTimeout(() => {
                router.push('/dashboard')
              }, 1000)
            } else {
              setStatus('error')
              setMessage('No session found. Please try logging in again.')
              
              setTimeout(() => {
                router.push('/login')
              }, 3000)
            }
          }, 1000)
        }
      } catch (error) {
        console.error('Unexpected error in auth callback:', error)
        setStatus('error')
        setMessage('An unexpected error occurred. Please try again.')
        
        setTimeout(() => {
          router.push('/login?error=unexpected_error')
        }, 3000)
      }
    }

    handleAuthCallback()
  }, [router])

  return (
    <div className="min-h-screen paper-bg flex items-center justify-center px-4">
      <div className="text-center space-y-4">
        {status === 'loading' && (
          <>
            <div className="mb-4">
              <Logo size="lg" showText={true} className="justify-center" />
            </div>
            <div className="w-12 h-12 mx-auto animate-spin rounded-full border-2 border-primary border-t-transparent" />
            <h2 className="text-xl font-heading">Completing authentication...</h2>
            <p className="text-muted-foreground font-sans">Please wait while we sign you in.</p>
          </>
        )}
        
        {status === 'success' && (
          <>
            <div className="w-12 h-12 mx-auto rounded-full bg-green-100 flex items-center justify-center">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-heading text-green-600">Authentication Successful!</h2>
            <p className="text-muted-foreground font-sans">{message}</p>
          </>
        )}
        
        {status === 'error' && (
          <>
            <div className="w-12 h-12 mx-auto rounded-full bg-red-100 flex items-center justify-center">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-heading text-red-600">Authentication Failed</h2>
            <p className="text-muted-foreground font-sans mb-4">{message}</p>
            
            {countdown > 0 && (
              <p className="text-sm text-muted-foreground font-sans mb-4">
                Redirecting to login page in {countdown} second{countdown !== 1 ? 's' : ''}...
              </p>
            )}
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={() => router.push('/login')}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors font-sans"
              >
                Try Again
              </button>
              <button
                onClick={() => router.push('/')}
                className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80 transition-colors font-sans"
              >
                Return Home
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen paper-bg flex items-center justify-center px-4">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 mx-auto animate-spin rounded-full border-2 border-primary border-t-transparent" />
          <h2 className="text-xl font-heading">Loading...</h2>
          <p className="text-muted-foreground font-sans">Please wait while we process your request.</p>
        </div>
      </div>
    }>
      <AuthCallbackComponent />
    </Suspense>
  )
}
