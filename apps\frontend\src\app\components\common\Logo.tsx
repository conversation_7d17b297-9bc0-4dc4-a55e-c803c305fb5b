import React from 'react';
import Image from 'next/image';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
}

export function Logo({ className = '', size = 'md', showText = false }: LogoProps) {
  const sizeMap = {
    sm: 32,
    md: 48,
    lg: 64,
    xl: 80
  };

  const dimensions = sizeMap[size];

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Image
        src="/Evolve.svg"
        alt="Evolve Logo"
        width={dimensions}
        height={dimensions}
        priority
        className="filter drop-shadow-sm"
      />
      {showText && (
        <span className="text-2xl font-heading text-primary font-bold">Evolve</span>
      )}
    </div>
  );
}
