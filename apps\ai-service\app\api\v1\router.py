"""
API Router for v1 endpoints

This module defines the main API router that includes all v1 endpoints
for the EVOLVE AI Service.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import ai, health

# Create the main API router
api_router = APIRouter()

# Include route modules
api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(ai.router, prefix="/ai", tags=["ai"])
