/**
 * Common types used across the EVOLVE platform
 */

export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface ApiResponse<T = any> {
  data: T
  success: boolean
  message?: string
  error?: string
}

export interface ErrorResponse {
  error: string
  message: string
  statusCode: number
  timestamp: string
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

export interface AsyncState<T> {
  data: T | null
  state: LoadingState
  error: string | null
}

export type Theme = 'light' | 'dark' | 'system'

export interface SelectOption<T = string> {
  label: string
  value: T
  disabled?: boolean
}

export interface MediaFile {
  id: string
  filename: string
  url: string
  mimeType: string
  size: number
  uploadedAt: Date
}
